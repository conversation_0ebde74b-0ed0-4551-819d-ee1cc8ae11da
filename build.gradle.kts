import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {

    id("org.springframework.boot") version "3.3.0"
    id("io.spring.dependency-management") version "1.1.5"
    id("org.jetbrains.kotlin.plugin.jpa") version "1.9.21"
    kotlin("jvm") version "1.9.21"
    kotlin("plugin.spring") version "1.9.21"
    kotlin("kapt") version "1.9.21"
}

group = "com.myprogressguru"
version = "0.0.1"
java.sourceCompatibility = JavaVersion.VERSION_21

extra["springCloudVersion"] = "2023.0.1"

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}


val gitLabPrivateToken: String = System.getenv().getOrElse("GITLAB_PRIVATE_TOKEN") {
    projectDir.resolve(".gitlab-private-token").readText().trim()
}


repositories {
    mavenCentral()
    maven {
        url = uri("https://gitlab.com/api/v4/groups/13592976/-/packages/maven")
        name = "GitLab"
        credentials(HttpHeaderCredentials::class) {
            name = "Private-Token"
            value = gitLabPrivateToken
        }
        authentication {
            create<HttpHeaderAuthentication>("header")
        }
    }
    maven { url = uri("https://repo.spring.io/milestone") }
    maven { url = uri("https://repo.spring.io/snapshot") }
    maven {
        name = "Central Portal Snapshots"
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
    }
}

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-client")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation(platform("org.springframework.ai:spring-ai-bom:1.0.0-M7"))
    implementation("org.springframework.ai:spring-ai-openai-spring-boot-starter:1.0.0-M6")
    implementation(platform("software.amazon.awssdk:bom:2.30.11"))
    implementation("software.amazon.awssdk:ssm")
    implementation("io.awspring.cloud:spring-cloud-aws-autoconfigure:3.0.2")
    implementation("io.awspring.cloud:spring-cloud-aws-sqs:3.0.2")
//    implementation("com.amazonaws:aws-java-sdk:1.12.172")
//    implementation("com.amazonaws:amazon-sqs-java-messaging-lib:1.0.8")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.2")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.2")
    implementation("org.flywaydb:flyway-core:9.16.0")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.hibernate:hibernate-validator:8.0.0.Final")
    implementation("org.passay:passay:1.6.2")
    implementation("org.freemarker:freemarker:2.3.31")
    implementation("io.github.microutils:kotlin-logging:3.0.5")
    implementation("com.querydsl:querydsl-jpa:5.0.0:jakarta")
    implementation("com.myprogressguru:exception-handler:0.0.30")
    implementation("com.myprogressguru:keycloak-utils:0.0.86")
    implementation("com.myprogressguru:common-utils:0.0.27")
    implementation("com.myprogressguru:storage-utils:0.0.19")
    implementation("com.myprogressguru:notifications-utils:0.0.12")
    implementation("io.hypersistence:hypersistence-utils-hibernate-63:3.7.5")
    compileOnly("org.mapstruct:mapstruct:1.5.5.Final")
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    runtimeOnly("org.postgresql:postgresql:42.5.4")
    runtimeOnly("io.micrometer:micrometer-registry-prometheus:1.10.5")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    annotationProcessor("com.querydsl:querydsl-apt:5.0.0:jakarta")
    kapt("org.mapstruct:mapstruct-processor:1.5.3.Final")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict", "-Xjvm-default=all")
        jvmTarget = "21"
    }
}

tasks.getByName<Jar>("jar") {
    enabled = false
}

tasks.withType<Test> {
    useJUnitPlatform()
}
