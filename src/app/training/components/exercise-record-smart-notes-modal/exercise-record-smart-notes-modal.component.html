<mpg-modal-layout-v2
  [scrollY]="false"
  [showFooter]="true"
  [title]="'exercises.' + exerciseRecord.exercise.exercise.id | translate: { fallback: exerciseRecord.exercise.exercise.name }"
  subtitle="smart-notes"
>
  <div class="set-selector flex-start">
    @for (setRecord of exerciseRecord.setRecords; track setRecord) {
      <div
        class="set flex-grow flex"
        (click)="handleSetRecordClick(setRecord)"
      >
        <ion-ripple-effect></ion-ripple-effect>
        <ion-label
          [class.selected]="setRecord === selectedSetRecord"
        > {{ setRecord.orderNumber }}
        </ion-label>
      </div>
    }
  </div>

  <div class="chat-input" footer>
    <ion-row>
      <ion-col offset-lg="3" size-lg="6">
        <div class="flex-centered chat-controls">
          <div class="attach flex-centered ion-activatable overflow-hidden" slot="start">
            <ion-ripple-effect></ion-ripple-effect>
            <ion-icon src="/assets/icon/duplicate.svg"></ion-icon>
          </div>
          <ion-input [(ngModel)]="message" [placeholder]="'chat.placeholder' | translate" labelPlacement="fixed"
                     type="text">
          </ion-input>
          @if (message) {
            <ion-icon class="send" name="send"></ion-icon>
          } @else {
            <ion-icon class="record" name="mic-outline"></ion-icon>
          }
        </div>
      </ion-col>
    </ion-row>
  </div>
</mpg-modal-layout-v2>
