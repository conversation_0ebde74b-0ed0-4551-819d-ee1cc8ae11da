<mpg-modal-layout-v2
  [scrollY]="true"
  [showFooter]="false"
  [title]="'exercises.' + exerciseRecord.exercise.exercise.id | translate: { fallback: exerciseRecord.exercise.exercise.name }"
  subtitle="smart-notes.label"
>
  <div class="chat-container">
    <div class="set-selector flex-start">
      @for (setRecord of exerciseRecord.setRecords; track setRecord) {
        <div
          class="set flex-grow flex"
          (click)="handleSetRecordClick(setRecord)"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label
            [class.selected]="setRecord === selectedSetRecord"
          > {{ setRecord.orderNumber }}
          </ion-label>
        </div>
      }
    </div>

    <div class="messages-container">
      <div #messagesArea class="messages-area">
        @for (group of groupedMessages; track group.creatorId + group.timestamp) {
          <div class="message-group" [class.own-group]="group.isCurrentUser">
            <!-- Group header with sender name and timestamp -->
            <div class="group-header">
              <span class="sender-name">&#64;{{ group.creatorName }}</span>
              <span class="group-time">{{ group.timestamp | date:'short' }}</span>
            </div>

            <!-- Messages in the group -->
            @for (message of group.messages; track message.id; let first = $first; let last = $last) {
              <div class="message"
                   [class.first-in-group]="first"
                   [class.last-in-group]="last"
                   [class.single-in-group]="group.messages.length === 1">
                <div class="message-wrapper"
                     (touchstart)="handleMessageHoldStart(message.id)"
                     (touchend)="handleMessageHoldEnd()"
                     (mousedown)="handleMessageHoldStart(message.id)"
                     (mouseup)="handleMessageHoldEnd()"
                     (mouseleave)="handleMessageHoldEnd()">
                  <div class="message-content">
                    @if (message.type === 'TEXT') {
                      <div class="message-text">{{ message.content }}</div>
                    } @else if (message.type === 'PHOTO') {
                      <img
                        [src]="message.mediaUrl"
                        alt="Shared image"
                        class="message-media clickable"
                        (click)="handlePhotoClick(message.mediaUrl)"
                        (load)="handleImageLoad($event, message.mediaUrl)"
                        (error)="handleImageError($event, message.mediaUrl)"
                      />
                    } @else if (message.type === 'VIDEO') {
                      <video
                        [src]="message.mediaUrl"
                        mpgVideoPlayer
                        class="message-media video-thumbnail"
                      ></video>
                    } @else if (message.type === 'AUDIO') {
                      <div class="voice-message">
                        <ion-button
                          fill="clear"
                          size="small"
                          class="voice-play-btn"
                          (click)="toggleVoicePlayback(message.id, message.mediaUrl)">
                          <ion-icon [name]="isVoicePlaying[message.id] ? 'pause' : 'play'"></ion-icon>
                        </ion-button>

                        <div class="voice-waveform" [id]="'waveform-' + message.id"></div>

                        <span class="voice-time">{{ getVoiceDurationDisplay(message.id) }}</span>
                      </div>
                    }
                  </div>

                  <!-- Delete button -->
                  @if (deleteButtonVisible[message.id]) {
                    <div class="delete-button-container">
                      <ion-button
                        fill="clear"
                        size="small"
                        color="danger"
                        class="delete-button"
                        (click)="handleDeleteMessage(message.id)">
                        <ion-icon name="trash-outline"></ion-icon>
                      </ion-button>
                      <ion-button
                        fill="clear"
                        size="small"
                        class="cancel-button"
                        (click)="hideDeleteButton(message.id)">
                        <ion-icon name="close-outline"></ion-icon>
                      </ion-button>
                    </div>
                  }
                </div>
              </div>
            }
          </div>
        }

        <!-- Upload progress indicator -->
        @if (uploadProgress) {
          <div class="upload-progress-container">
            <div class="upload-progress-card">
              <div class="upload-info">
                <ion-icon name="cloud-upload-outline"></ion-icon>
                <span class="upload-text">{{ 'smart-notes.uploading' | translate }}</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="uploadProgress.progress"></div>
              </div>
              <span class="progress-text">{{ uploadProgress.progress }}%</span>
            </div>
          </div>
        }
      </div>
    </div>
  </div>

  <div class="chat-input">
    <!-- Hidden file input for media selection -->
    <input
      #fileInput
      (change)="handleFileSelect($event)"
      accept="image/*,video/*"
      style="display: none"
      type="file"
    />

    <!-- Recording interface (replaces input when recording) -->
    @if (isRecording) {
      <div class="recording-interface">
        <div class="recording-status">
          <div class="recording-timer">{{ formatDuration(recordingDuration) }}</div>
          <div class="recording-visualizer">
            <div class="sound-waves">
              @for (bar of realtimeSoundBars; track $index) {
                <div class="sound-bar" [style.height.px]="bar"></div>
              }
            </div>
          </div>
        </div>
        <div class="recording-actions">
          <ion-button fill="clear" class="discard-btn" (click)="cancelRecording()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
          <ion-button fill="clear" class="done-btn" (click)="sendRecording()">
            <ion-icon name="checkmark"></ion-icon>
          </ion-button>
        </div>
      </div>
    } @else {
      <!-- Normal input interface -->
      <div class="flex-centered chat-controls">
        <div (click)="triggerFileSelect()" class="attach flex-centered ion-activatable overflow-hidden">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon src="/assets/icon/duplicate.svg"></ion-icon>
        </div>
        <ion-input
          (keyup.enter)="handleSendMessage()"
          [(ngModel)]="message"
          [disabled]="isLoading"
          [placeholder]="'chat.placeholder' | translate"
          labelPlacement="fixed"
          type="text"
        >
        </ion-input>
        @if (isLoading) {
          <ion-spinner name="dots"></ion-spinner>
        } @else if (message) {
          <ion-icon class="send" name="send" (click)="handleSendMessage()"></ion-icon>
        } @else {
          <ion-icon class="record" name="mic-outline" (click)="handleStartRecording()"></ion-icon>
        }
      </div>
    }
  </div>
</mpg-modal-layout-v2>
