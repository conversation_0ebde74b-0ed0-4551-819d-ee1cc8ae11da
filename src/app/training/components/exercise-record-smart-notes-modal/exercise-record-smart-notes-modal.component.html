<mpg-modal-layout-v2
  [scrollY]="true"
  [showFooter]="false"
  [title]="'exercises.' + exerciseRecord.exercise.exercise.id | translate: { fallback: exerciseRecord.exercise.exercise.name }"
  subtitle="smart-notes.label"
>
  <div class="chat-container">
    <div class="set-selector flex-start">
      @for (setRecord of exerciseRecord.setRecords; track setRecord) {
        <div
          class="set flex-grow flex"
          (click)="handleSetRecordClick(setRecord)"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label
            [class.selected]="setRecord === selectedSetRecord"
          > {{ setRecord.orderNumber }}
          </ion-label>
        </div>
      }
    </div>

    <!-- Reusable Smart Notes Chat Component -->
    <app-smart-notes-chat
      [smartNotes]="smartNotes"
      [currentUserId]="currentUserId"
      [placeholder]="'smart-notes.placeholder' | translate"
      [allowFileUpload]="true"
      [allowVoiceRecording]="true"
      [maxRecordingDuration]="300"
      (noteAdded)="onNoteAdded($event)"
      (notesUpdated)="onNotesUpdated($event)"
    ></app-smart-notes-chat>
  </div>
</mpg-modal-layout-v2>
