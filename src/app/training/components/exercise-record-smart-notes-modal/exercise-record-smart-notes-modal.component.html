<mpg-modal-layout-v2
  [scrollY]="true"
  [showFooter]="true"
  [title]="'exercises.' + exerciseRecord.exercise.exercise.id | translate: { fallback: exerciseRecord.exercise.exercise.name }"
  subtitle="smart-notes"
>
  <div class="chat-container">
    <div class="set-selector flex-start">
      @for (setRecord of exerciseRecord.setRecords; track setRecord) {
        <div
          class="set flex-grow flex"
          (click)="handleSetRecordClick(setRecord)"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label
            [class.selected]="setRecord === selectedSetRecord"
          > {{ setRecord.orderNumber }}
          </ion-label>
        </div>
      }
    </div>

    <div class="messages-container">
      <div class="messages-area">
        @for (smartNote of smartNotes; track smartNote.id) {
          <div class="message" [class.own-message]="isCurrentUser(smartNote)">
            <div class="message-content">
              <div class="message-text">{{ smartNote.content }}</div>
              <div class="message-time">{{ smartNote.createdOn | date:'short' }}</div>
            </div>
          </div>
        }
        @if (smartNotes.length === 0) {
          <div class="empty-state">
            <ion-label>{{ 'smart-notes.empty-state' | translate }}</ion-label>
          </div>
        }
      </div>
    </div>
  </div>

  <div class="chat-input" slot="footer">
    <div class="flex-centered chat-controls">
      <div class="attach flex-centered ion-activatable overflow-hidden">
        <ion-ripple-effect></ion-ripple-effect>
        <ion-icon src="/assets/icon/duplicate.svg"></ion-icon>
      </div>
      <ion-input
        (keyup.enter)="handleSendMessage()"
        [(ngModel)]="message"
        [placeholder]="'chat.placeholder' | translate"
        [disabled]="isLoading"
        labelPlacement="fixed"
        type="text"
      >
      </ion-input>
      @if (isLoading) {
        <ion-spinner name="dots"></ion-spinner>
      } @else if (message) {
        <ion-icon class="send" name="send" (click)="handleSendMessage()"></ion-icon>
      } @else {
        <ion-icon class="record" name="mic-outline" (click)="handleStartRecording()"></ion-icon>
      }
    </div>
  </div>
</mpg-modal-layout-v2>
