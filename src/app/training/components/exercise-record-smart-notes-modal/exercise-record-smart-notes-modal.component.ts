import { Component, Input, OnInit } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import { ModalService, SmartNoteService } from '../../../shared/services';
import { SmartNote, SmartNoteCreateRequest } from '../../../shared/models';
import {
  WorkoutExerciseSetRecordService,
  WorkoutRecordService,
} from '../../services';
import { UserService } from '../../../auth/services';
import { SmartNoteType } from '../../../shared/enumerations';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss'],
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;

  message: string;
  smartNotes: SmartNote[] = [];
  isRecording = false;
  isLoading = false;
  currentUserId: string;

  // Grouped messages for Instagram-style display
  get groupedMessages(): MessageGroup[] {
    return this.groupMessagesBySender(this.smartNotes);
  }

  constructor(
    private modalService: ModalService,
    private workoutRecordService: WorkoutRecordService,
    private smartNoteService: SmartNoteService,
    private userService: UserService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
  ) {}

  ngOnInit(): void {
    this.loadSmartNotes();
    this.loadCurrentUserId();
  }

  isCurrentUser(smartNote: SmartNote): boolean {
    return smartNote.creatorId === this.currentUserId;
  }

  handleSetRecordClick(setRecord: WorkoutExerciseSetRecord) {
    this.selectedSetRecord = setRecord;
    this.loadSmartNotes();
  }

  handleSendMessage(): void {
    if (!this.message?.trim() || this.isLoading) {
      return;
    }

    this.isLoading = true;

    const createRequest: SmartNoteCreateRequest = {
      type: SmartNoteType.TEXT,
      content: this.message.trim(),
    };

    const workoutRecordId = this.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    this.workoutRecordService
      .createSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        createRequest,
      )
      .subscribe({
        next: (smartNote) => {
          this.selectedSetRecord.smartNotes =
            this.selectedSetRecord.smartNotes.concat(smartNote);

          this.message = '';
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error creating smart note:', error);
          this.isLoading = false;
        },
      });
  }

  handleStartRecording(): void {
    // TODO: Implement voice recording functionality
    console.log('Start recording...');
  }

  private loadCurrentUserId(): void {
    this.userService.loggedUserId$.subscribe((userId) => {
      this.currentUserId = userId;
    });
  }

  private loadSmartNotes(): void {
    if (!this.selectedSetRecord) {
      this.selectedSetRecord = this.exerciseRecord.setRecords[0];
    }
  }
}
