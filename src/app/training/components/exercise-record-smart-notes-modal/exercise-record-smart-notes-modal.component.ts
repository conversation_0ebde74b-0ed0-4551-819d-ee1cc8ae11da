import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import {
  FileService,
  ModalService,
  SmartNoteService,
  ToastService,
} from '../../../shared/services';
import {
  SmartNote,
  SmartNoteCreateRequest,
  StorageObjectInProgress,
} from '../../../shared/models';
import {
  WorkoutExerciseSetRecordService,
  WorkoutRecordService,
} from '../../services';
import { UserService } from '../../../auth/services';
import { SmartNoteType } from '../../../shared/enumerations';
import { environment } from '../../../../environments/environment';
import { of, switchMap } from 'rxjs';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss'],
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  @ViewChild('messagesArea') messagesArea: ElementRef<HTMLDivElement>;

  message: string;
  smartNotes: SmartNote[] = [];
  isRecording = false;
  isLoading = false;
  currentUserId: string;
  uploadProgress: StorageObjectInProgress | null = null;
  deleteButtonVisible: { [messageId: string]: boolean } = {};
  deleteHoldTimer: any;

  // Voice recording properties
  mediaRecorder: MediaRecorder | null = null;
  audioChunks: Blob[] = [];
  recordingDuration = 0;
  recordingTimer: any;
  maxRecordingDuration = 300; // 5 minutes max

  // Voice playback properties
  isVoicePlaying: { [messageId: string]: boolean } = {};
  voiceProgress: { [messageId: string]: number } = {};
  voiceDuration: { [messageId: string]: number } = {}; // Store as seconds
  voiceCurrentTime: { [messageId: string]: number } = {};
  audioElements: { [messageId: string]: HTMLAudioElement } = {};

  // Instagram-style sound wave bars (random heights for visual effect)
  soundWaveBars: number[] = [
    12, 8, 15, 6, 18, 10, 14, 7, 16, 9, 13, 11, 17, 5, 19, 8, 12, 14, 6, 15,
    9, 11, 13, 7, 16, 10, 18, 12, 8, 14, 6, 17, 9, 15, 11, 13, 7, 19, 10, 16
  ];

  constructor(
    private modalService: ModalService,
    private workoutRecordService: WorkoutRecordService,
    private smartNoteService: SmartNoteService,
    private userService: UserService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
    private fileService: FileService,
    private toastService: ToastService,
  ) {}

  // Grouped messages for Instagram-style display
  get groupedMessages(): MessageGroup[] {
    return this.groupMessagesBySender(this.smartNotes);
  }

  ngOnInit(): void {
    this.loadSmartNotes();
    this.loadCurrentUserId();
    // Auto-scroll to bottom after initial load
    setTimeout(() => this.scrollToBottom(), 200);
  }

  isCurrentUser(smartNote: SmartNote): boolean {
    return smartNote.creatorId === this.currentUserId;
  }

  handleSetRecordClick(setRecord: WorkoutExerciseSetRecord) {
    this.selectedSetRecord = setRecord;
    this.loadSmartNotes();
    // Auto-scroll to bottom when switching sets
    setTimeout(() => this.scrollToBottom(), 100);
  }

  handleSendMessage(): void {
    if (!this.message?.trim() || this.isLoading) {
      return;
    }

    this.isLoading = true;

    const createRequest: SmartNoteCreateRequest = {
      type: SmartNoteType.TEXT,
      content: this.message.trim(),
      mediaId: '', // Empty for text messages
    };

    const workoutRecordId = this.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    this.workoutRecordService
      .createSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        createRequest,
      )
      .subscribe({
        next: (smartNote) => {
          // Add the new smart note to the local array using functional approach
          this.smartNotes = [...this.smartNotes, smartNote];

          // Also update the selected set record's smart notes array
          this.selectedSetRecord.smartNotes = [...this.smartNotes];

          this.message = '';
          this.isLoading = false;

          // Auto-scroll to bottom
          this.scrollToBottom();
        },
        error: (error) => {
          console.error('Error creating smart note:', error);
          this.isLoading = false;
        },
      });
  }

  async handleStartRecording(): Promise<void> {
    if (this.isRecording) {
      this.stopRecording();
      return;
    }

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      // Create MediaRecorder instance
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: this.getSupportedMimeType()
      });

      this.audioChunks = [];
      this.recordingDuration = 0;
      this.isRecording = true;

      // Handle data available event
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      // Handle recording stop event
      this.mediaRecorder.onstop = () => {
        this.processRecording();
        stream.getTracks().forEach(track => track.stop()); // Stop microphone access
      };

      // Start recording
      this.mediaRecorder.start(1000); // Collect data every second

      // Start timer
      this.startRecordingTimer();

      this.toastService.showInfoToast('smart-notes.recording-started');
    } catch (error) {
      console.error('Error starting recording:', error);
      this.toastService.showInfoToast('smart-notes.recording-error');
    }
  }

  stopRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
    }
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // Fallback
  }

  private startRecordingTimer(): void {
    this.recordingTimer = setInterval(() => {
      this.recordingDuration++;

      // Auto-stop at max duration
      if (this.recordingDuration >= this.maxRecordingDuration) {
        this.stopRecording();
        this.toastService.showInfoToast('smart-notes.recording-max-duration');
      }
    }, 1000);
  }

  private clearRecordingTimer(): void {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }

  private processRecording(): void {
    if (this.audioChunks.length === 0) {
      this.toastService.showInfoToast('smart-notes.recording-empty');
      return;
    }

    // Create audio blob
    const audioBlob = new Blob(this.audioChunks, {
      type: this.getSupportedMimeType()
    });

    // Upload the audio file
    this.uploadAudioFile(audioBlob);
  }

  private uploadAudioFile(audioBlob: Blob): void {
    // Create a File object from the blob
    const audioFile = new File([audioBlob], `voice-message-${Date.now()}.webm`, {
      type: audioBlob.type
    });

    // Create a fake file input event
    const fakeEvent = {
      target: {
        files: [audioFile]
      }
    };

    // Start upload with progress tracking
    this.fileService
      .uploadFile(
        fakeEvent,
        this.smartNoteService.getMediaUploadUrl(environment.TRAINING_SERVICE_API_URL),
        environment.TRAINING_SERVICE_API_URL,
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Show upload progress
            this.uploadProgress = storageObject;
            setTimeout(() => this.scrollToBottom(), 100);
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: SmartNoteType.AUDIO,
            content: `Voice message (${this.formatDuration(this.recordingDuration)})`,
            mediaId: storageObject.id,
          };

          const workoutRecordId = this.exerciseRecord.workoutRecord.id;
          const exerciseRecordId = this.exerciseRecord.id;
          const setRecordId = this.selectedSetRecord.id;

          return this.workoutRecordService
            .createSetRecordSmartNote(
              workoutRecordId,
              exerciseRecordId,
              setRecordId,
              createRequest,
            )
            .pipe(
              switchMap((smartNote) => {
                // Clear upload progress
                this.uploadProgress = null;

                // Add to local array
                this.smartNotes = [...this.smartNotes, smartNote];
                this.selectedSetRecord.smartNotes = [...this.smartNotes];

                // Show success toast
                this.toastService.showInfoToast('smart-notes.voice-upload-success');

                // Auto-scroll to bottom
                this.scrollToBottom();

                return of(smartNote);
              }),
            );
        }),
      )
      .subscribe({
        error: (error) => {
          console.error('Error uploading voice message:', error);
          this.uploadProgress = null;
          this.toastService.showInfoToast('smart-notes.voice-upload-error');
        },
      });
  }

  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Voice message playback methods
  toggleVoicePlayback(messageId: string, audioUrl: string): void {
    const audio = this.audioElements[messageId];

    if (!audio) {
      // Create new audio element if it doesn't exist
      const newAudio = new Audio(audioUrl);
      this.audioElements[messageId] = newAudio;

      newAudio.addEventListener('loadedmetadata', () => {
        if (newAudio.duration && !isNaN(newAudio.duration) && isFinite(newAudio.duration)) {
          this.voiceDuration[messageId] = Math.floor(newAudio.duration);
        } else {
          this.voiceDuration[messageId] = 0;
        }
      });

      newAudio.addEventListener('timeupdate', () => {
        const progress = (newAudio.currentTime / newAudio.duration) * 100;
        this.voiceProgress[messageId] = progress;
      });

      newAudio.addEventListener('ended', () => {
        this.isVoicePlaying[messageId] = false;
        this.voiceProgress[messageId] = 0;
        newAudio.currentTime = 0;
      });

      // Start playing
      newAudio.play();
      this.isVoicePlaying[messageId] = true;
    } else {
      // Toggle existing audio
      if (this.isVoicePlaying[messageId]) {
        audio.pause();
        this.isVoicePlaying[messageId] = false;
      } else {
        audio.play();
        this.isVoicePlaying[messageId] = true;
      }
    }
  }

  onAudioLoaded(messageId: string, event: any): void {
    const audio = event.target as HTMLAudioElement;
    this.audioElements[messageId] = audio;

    // Check if duration is valid
    if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
      this.voiceDuration[messageId] = Math.floor(audio.duration);
    } else {
      // Fallback duration if metadata is not available
      this.voiceDuration[messageId] = 0;
    }
  }

  onAudioTimeUpdate(messageId: string, event: any): void {
    const audio = event.target as HTMLAudioElement;

    if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
      const progress = (audio.currentTime / audio.duration) * 100;
      this.voiceProgress[messageId] = progress;
      this.voiceCurrentTime[messageId] = Math.floor(audio.currentTime);
    }
  }

  onAudioEnded(messageId: string): void {
    this.isVoicePlaying[messageId] = false;
    this.voiceProgress[messageId] = 0;
    this.voiceCurrentTime[messageId] = 0;
    if (this.audioElements[messageId]) {
      this.audioElements[messageId].currentTime = 0;
    }
  }

  onAudioError(messageId: string, event: any): void {
    console.error('Audio error for message', messageId, event);
    this.isVoicePlaying[messageId] = false;
    this.voiceProgress[messageId] = 0;
    this.voiceCurrentTime[messageId] = 0;
  }

  getVoiceProgress(messageId: string): number {
    return this.voiceProgress[messageId] || 0;
  }

  getVoiceDurationDisplay(messageId: string): string {
    const duration = this.voiceDuration[messageId];
    const currentTime = this.voiceCurrentTime[messageId] || 0;

    if (this.isVoicePlaying[messageId] && currentTime > 0) {
      return this.formatDuration(currentTime);
    } else if (duration && duration > 0) {
      return this.formatDuration(duration);
    } else {
      return '0:00';
    }
  }

  getWaveBarActive(messageId: string, barIndex: number): boolean {
    const progress = this.voiceProgress[messageId] || 0;
    const totalBars = this.soundWaveBars.length;
    const activeBarCount = Math.floor((progress / 100) * totalBars);
    return barIndex < activeBarCount;
  }

  handlePhotoClick(photoUrl: string): void {
    this.modalService.showPhoto(photoUrl);
  }

  handleMessageHoldStart(messageId: string): void {
    this.deleteHoldTimer = setTimeout(() => {
      this.deleteButtonVisible[messageId] = true;
    }, 500); // Show delete button after 500ms hold
  }

  handleMessageHoldEnd(): void {
    if (this.deleteHoldTimer) {
      clearTimeout(this.deleteHoldTimer);
      this.deleteHoldTimer = null;
    }
  }

  handleDeleteMessage(messageId: string): void {
    this.smartNoteService
      .delete(environment.TRAINING_SERVICE_API_URL, messageId)
      .subscribe({
        next: () => {
          // Remove from local array using functional approach
          this.smartNotes = this.smartNotes.filter(
            (note) => note.id !== messageId,
          );
          this.selectedSetRecord.smartNotes = [...this.smartNotes];

          // Hide delete button
          delete this.deleteButtonVisible[messageId];

          this.toastService.showInfoToast('smart-notes.delete-success');
        },
        error: (error) => {
          console.error('Error deleting smart note:', error);
          this.toastService.showInfoToast('smart-notes.delete-error');
        },
      });
  }

  hideDeleteButton(messageId: string): void {
    delete this.deleteButtonVisible[messageId];
  }

  triggerFileSelect(): void {
    this.fileInput.nativeElement.click();
  }

  handleFileSelect(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Determine smart note type based on file type
    const isVideo = file.type.startsWith('video/');
    const smartNoteType = isVideo ? SmartNoteType.VIDEO : SmartNoteType.PHOTO;

    // Start upload with progress tracking
    this.fileService
      .uploadFile(
        event,
        this.smartNoteService.getMediaUploadUrl(
          environment.TRAINING_SERVICE_API_URL,
        ),
        environment.TRAINING_SERVICE_API_URL,
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Show upload progress
            this.uploadProgress = storageObject;
            // Scroll to bottom after progress is shown
            setTimeout(() => this.scrollToBottom(), 100);
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: smartNoteType,
            content: '', // Empty for media messages
            mediaId: storageObject.id,
          };

          const workoutRecordId = this.exerciseRecord.workoutRecord.id;
          const exerciseRecordId = this.exerciseRecord.id;
          const setRecordId = this.selectedSetRecord.id;

          return this.workoutRecordService
            .createSetRecordSmartNote(
              workoutRecordId,
              exerciseRecordId,
              setRecordId,
              createRequest,
            )
            .pipe(
              switchMap((smartNote) => {
                // Clear upload progress
                this.uploadProgress = null;

                // Add to local array
                this.smartNotes = [...this.smartNotes, smartNote];
                this.selectedSetRecord.smartNotes = [...this.smartNotes];

                // Show success toast
                this.toastService.showInfoToast('smart-notes.upload-success');

                // Clear file input
                this.fileInput.nativeElement.value = '';

                // Auto-scroll to bottom
                this.scrollToBottom();

                return of(smartNote);
              }),
            );
        }),
      )
      .subscribe({
        error: (error) => {
          console.error('Error uploading file:', error);
          this.uploadProgress = null;
          this.fileInput.nativeElement.value = '';
          this.toastService.showInfoToast('smart-notes.upload-error');
        },
      });
  }

  // Method to handle S3 image loading with retry logic
  handleImageLoad(event: any, imageUrl: string): void {
    const img = event.target as HTMLImageElement;

    // If image failed to load (404), retry after a delay
    if (img.naturalWidth === 0) {
      this.retryImageLoad(img, imageUrl, 0);
    }
  }

  handleImageError(event: any, imageUrl: string): void {
    const img = event.target as HTMLImageElement;
    this.retryImageLoad(img, imageUrl, 0);
  }

  private loadCurrentUserId(): void {
    this.userService.loggedUserId$.subscribe((userId) => {
      this.currentUserId = userId;
    });
  }

  private loadSmartNotes(): void {
    if (!this.selectedSetRecord) {
      this.selectedSetRecord = this.exerciseRecord.setRecords[0];
    }

    // Use functional approach to ensure immutability
    this.smartNotes = this.selectedSetRecord.smartNotes
      ? [...this.selectedSetRecord.smartNotes]
      : [];
  }

  private groupMessagesBySender(messages: SmartNote[]): MessageGroup[] {
    if (!messages.length) return [];

    const groups: MessageGroup[] = [];
    let currentGroup: MessageGroup | null = null;

    // Sort messages by creation time
    const sortedMessages = [...messages].sort(
      (a, b) =>
        new Date(a.createdOn).getTime() - new Date(b.createdOn).getTime(),
    );

    sortedMessages.forEach((message) => {
      const isCurrentUser = this.isCurrentUser(message);
      const messageTime = new Date(message.createdOn);

      // Check if we should start a new group
      const shouldStartNewGroup =
        !currentGroup ||
        currentGroup.creatorId !== message.creatorId ||
        this.shouldBreakGroup(currentGroup.timestamp, message.createdOn);

      if (shouldStartNewGroup) {
        // Start new group
        currentGroup = {
          creatorId: message.creatorId,
          creatorName: message.creatorName,
          timestamp: message.createdOn,
          isCurrentUser,
          messages: [message],
        };
        groups.push(currentGroup);
      } else {
        // Add to existing group
        currentGroup.messages.push(message);
      }
    });

    return groups;
  }

  private shouldBreakGroup(
    lastTimestamp: string,
    currentTimestamp: string,
  ): boolean {
    const lastTime = new Date(lastTimestamp);
    const currentTime = new Date(currentTimestamp);
    const timeDiff = currentTime.getTime() - lastTime.getTime();

    // Break group if more than 5 minutes apart
    return timeDiff > 5 * 60 * 1000;
  }

  private scrollToBottom(): void {
    if (this.messagesArea?.nativeElement) {
      setTimeout(() => {
        const element = this.messagesArea.nativeElement;
        element.scrollTop = element.scrollHeight;
      }, 50);
    }
  }

  private retryImageLoad(
    img: HTMLImageElement,
    imageUrl: string,
    attempt: number,
  ): void {
    const maxRetries = 5;
    const retryDelay = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s

    if (attempt < maxRetries) {
      setTimeout(() => {
        // Force reload by adding timestamp
        const urlWithTimestamp = `${imageUrl}?t=${Date.now()}`;
        img.src = urlWithTimestamp;

        // Check if image loaded after a short delay
        setTimeout(() => {
          if (img.naturalWidth === 0) {
            this.retryImageLoad(img, imageUrl, attempt + 1);
          }
        }, 500);
      }, retryDelay);
    }
  }
}
