import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import {
  FileService,
  ModalService,
  SmartNoteService,
  ToastService,
} from '../../../shared/services';
import {
  SmartNote,
  SmartNoteCreateRequest,
  StorageObjectInProgress,
} from '../../../shared/models';
import {
  WorkoutExerciseSetRecordService,
  WorkoutRecordService,
} from '../../services';
import { UserService } from '../../../auth/services';
import { SmartNoteType } from '../../../shared/enumerations';
import { environment } from '../../../../environments/environment';
import { of, switchMap } from 'rxjs';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss'],
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  @ViewChild('messagesArea') messagesArea: ElementRef<HTMLDivElement>;

  message: string;
  smartNotes: SmartNote[] = [];
  isRecording = false;
  isLoading = false;
  currentUserId: string;
  uploadProgress: StorageObjectInProgress | null = null;

  constructor(
    private modalService: ModalService,
    private workoutRecordService: WorkoutRecordService,
    private smartNoteService: SmartNoteService,
    private userService: UserService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
    private fileService: FileService,
    private toastService: ToastService,
  ) {}

  // Grouped messages for Instagram-style display
  get groupedMessages(): MessageGroup[] {
    return this.groupMessagesBySender(this.smartNotes);
  }

  ngOnInit(): void {
    this.loadSmartNotes();
    this.loadCurrentUserId();
    // Auto-scroll to bottom after initial load
    setTimeout(() => this.scrollToBottom(), 200);
  }

  isCurrentUser(smartNote: SmartNote): boolean {
    return smartNote.creatorId === this.currentUserId;
  }

  handleSetRecordClick(setRecord: WorkoutExerciseSetRecord) {
    this.selectedSetRecord = setRecord;
    this.loadSmartNotes();
    // Auto-scroll to bottom when switching sets
    setTimeout(() => this.scrollToBottom(), 100);
  }

  handleSendMessage(): void {
    if (!this.message?.trim() || this.isLoading) {
      return;
    }

    this.isLoading = true;

    const createRequest: SmartNoteCreateRequest = {
      type: SmartNoteType.TEXT,
      content: this.message.trim(),
      mediaId: '', // Empty for text messages
    };

    const workoutRecordId = this.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    this.workoutRecordService
      .createSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        createRequest,
      )
      .subscribe({
        next: (smartNote) => {
          // Add the new smart note to the local array using functional approach
          this.smartNotes = [...this.smartNotes, smartNote];

          // Also update the selected set record's smart notes array
          this.selectedSetRecord.smartNotes = [...this.smartNotes];

          this.message = '';
          this.isLoading = false;

          // Auto-scroll to bottom
          this.scrollToBottom();
        },
        error: (error) => {
          console.error('Error creating smart note:', error);
          this.isLoading = false;
        },
      });
  }

  handleStartRecording(): void {
    // TODO: Implement voice recording functionality
    console.log('Start recording...');
  }

  triggerFileSelect(): void {
    this.fileInput.nativeElement.click();
  }

  handleFileSelect(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Scroll to bottom when file is selected
    this.scrollToBottom();

    // Determine smart note type based on file type
    const isVideo = file.type.startsWith('video/');
    const smartNoteType = isVideo ? SmartNoteType.VIDEO : SmartNoteType.PHOTO;

    // Start upload with progress tracking
    this.fileService
      .uploadFile(
        event,
        this.smartNoteService.getMediaUploadUrl(
          environment.TRAINING_SERVICE_API_URL,
        ),
        environment.TRAINING_SERVICE_API_URL,
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Show upload progress
            this.uploadProgress = storageObject;
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: smartNoteType,
            content: '', // Empty for media messages
            mediaId: storageObject.id,
          };

          const workoutRecordId = this.exerciseRecord.workoutRecord.id;
          const exerciseRecordId = this.exerciseRecord.id;
          const setRecordId = this.selectedSetRecord.id;

          return this.workoutRecordService
            .createSetRecordSmartNote(
              workoutRecordId,
              exerciseRecordId,
              setRecordId,
              createRequest,
            )
            .pipe(
              switchMap((smartNote) => {
                // Clear upload progress
                this.uploadProgress = null;

                // Add to local array
                this.smartNotes = [...this.smartNotes, smartNote];
                this.selectedSetRecord.smartNotes = [...this.smartNotes];

                // Show success toast
                this.toastService.showInfoToast('smart-notes.upload-success');

                // Clear file input
                this.fileInput.nativeElement.value = '';

                // Auto-scroll to bottom
                this.scrollToBottom();

                return of(smartNote);
              }),
            );
        }),
      )
      .subscribe({
        error: (error) => {
          console.error('Error uploading file:', error);
          this.uploadProgress = null;
          this.fileInput.nativeElement.value = '';
          this.toastService.showInfoToast('smart-notes.upload-error');
        },
      });
  }

  private loadCurrentUserId(): void {
    this.userService.loggedUserId$.subscribe((userId) => {
      this.currentUserId = userId;
    });
  }

  private loadSmartNotes(): void {
    if (!this.selectedSetRecord) {
      this.selectedSetRecord = this.exerciseRecord.setRecords[0];
    }

    // Use functional approach to ensure immutability
    this.smartNotes = this.selectedSetRecord.smartNotes
      ? [...this.selectedSetRecord.smartNotes]
      : [];
  }

  private groupMessagesBySender(messages: SmartNote[]): MessageGroup[] {
    if (!messages.length) return [];

    const groups: MessageGroup[] = [];
    let currentGroup: MessageGroup | null = null;

    // Sort messages by creation time
    const sortedMessages = [...messages].sort(
      (a, b) =>
        new Date(a.createdOn).getTime() - new Date(b.createdOn).getTime(),
    );

    sortedMessages.forEach((message) => {
      const isCurrentUser = this.isCurrentUser(message);
      const messageTime = new Date(message.createdOn);

      // Check if we should start a new group
      const shouldStartNewGroup =
        !currentGroup ||
        currentGroup.creatorId !== message.creatorId ||
        this.shouldBreakGroup(currentGroup.timestamp, message.createdOn);

      if (shouldStartNewGroup) {
        // Start new group
        currentGroup = {
          creatorId: message.creatorId,
          creatorName: message.creatorName,
          timestamp: message.createdOn,
          isCurrentUser,
          messages: [message],
        };
        groups.push(currentGroup);
      } else {
        // Add to existing group
        currentGroup.messages.push(message);
      }
    });

    return groups;
  }

  private shouldBreakGroup(
    lastTimestamp: string,
    currentTimestamp: string,
  ): boolean {
    const lastTime = new Date(lastTimestamp);
    const currentTime = new Date(currentTimestamp);
    const timeDiff = currentTime.getTime() - lastTime.getTime();

    // Break group if more than 5 minutes apart
    return timeDiff > 5 * 60 * 1000;
  }

  private scrollToBottom(): void {
    if (this.messagesArea?.nativeElement) {
      setTimeout(() => {
        const element = this.messagesArea.nativeElement;
        element.scrollTop = element.scrollHeight;
      }, 50);
    }
  }
}
