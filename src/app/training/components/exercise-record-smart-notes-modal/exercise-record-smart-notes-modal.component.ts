import { Component, Input, OnInit } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import { ModalService, SmartNoteService } from '../../../shared/services';
import { SmartNote } from '../../../shared/models';
import { WorkoutRecordService } from '../../services';
import { UserService } from '../../../auth/services';

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss'],
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;

  message: string;
  smartNotes: SmartNote[] = [];
  isRecording = false;
  isLoading = false;
  currentUserId: string;

  constructor(
    private modalService: ModalService,
    private workoutRecordService: WorkoutRecordService,
    private smartNoteService: SmartNoteService,
    private userService: UserService,
  ) {}

  ngOnInit(): void {
    this.loadSmartNotes();
    this.loadCurrentUserId();
  }

  isCurrentUser(smartNote: SmartNote): boolean {
    return smartNote.creatorId === this.currentUserId;
  }

  handleSetRecordClick(setRecord: WorkoutExerciseSetRecord) {
    this.selectedSetRecord = setRecord;
    this.loadSmartNotes();
  }

  handleSendMessage(): void {
    if (!this.message?.trim()) {
      return;
    }

    // TODO: Implement actual message sending logic
    // For now, we'll just add it to the local array
    const newSmartNote: SmartNote = {
      id: Date.now().toString(), // Temporary ID
      content: this.message.trim(),
      createdAt: new Date(),
      creatorId: this.currentUserId,
      // Add other required SmartNote properties as needed
    } as SmartNote;

    this.smartNotes = [...this.smartNotes, newSmartNote];
    this.message = '';
  }

  handleStartRecording(): void {
    // TODO: Implement voice recording functionality
    console.log('Start recording...');
  }

  private loadCurrentUserId(): void {
    this.userService.loggedUserId$.subscribe((userId) => {
      this.currentUserId = userId;
    });
  }

  private loadSmartNotes(): void {
    if (!this.selectedSetRecord) {
      this.selectedSetRecord = this.exerciseRecord.setRecords[0];
    }

    this.smartNotes = this.selectedSetRecord.smartNotes || [];
  }
}
