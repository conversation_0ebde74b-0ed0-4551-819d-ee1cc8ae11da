import {Component, ElementRef, Input, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {WorkoutExerciseRecord, WorkoutExerciseSetRecord} from '../../models';
import {FileService, ModalService, SmartNoteService, ToastService,} from '../../../shared/services';
import {SmartNote, SmartNoteCreateRequest, StorageObject, StorageObjectInProgress, StorageObjectUploadUrlRequest,} from '../../../shared/models';
import {WorkoutExerciseSetRecordService, WorkoutRecordService,} from '../../services';
import {UserService} from '../../../auth/services';
import {SmartNoteType} from '../../../shared/enumerations';
import {environment} from '../../../../environments/environment';
import {Observable, of, switchMap} from 'rxjs';
import WaveSurfer from 'wavesurfer.js';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss']
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit, OnDestroy {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  @ViewChild('messagesArea') messagesArea: ElementRef<HTMLDivElement>;

  message: string;
  smartNotes: SmartNote[] = [];
  isRecording = false;
  isLoading = false;
  isInputFocused = false;
  currentUserId: string;
  uploadProgress: StorageObjectInProgress | null = null;
  trainingServiceUrl = environment.TRAINING_SERVICE_API_URL;
  deleteButtonVisible: { [messageId: string]: boolean } = {};
  deleteHoldTimer: any;

  // Voice recording properties
  mediaRecorder: MediaRecorder | null = null;
  audioChunks: Blob[] = [];
  recordingDuration = 0;
  recordingTimer: any;
  maxRecordingDuration = 300;
  recordingCancelled = false;

  // Real-time recording visualization
  realtimeSoundBars: number[] = Array(20).fill(2);
  audioContext: AudioContext | null = null;
  analyser: AnalyserNode | null = null;
  dataArray: Uint8Array | null = null;
  animationFrame: number | null = null;

  // Voice playback properties
  isVoicePlaying: { [messageId: string]: boolean } = {};
  voiceProgress: { [messageId: string]: number } = {};
  voiceDuration: { [messageId: string]: number } = {}; // Store as seconds
  voiceCurrentTime: { [messageId: string]: number } = {};
  audioElements: { [messageId: string]: HTMLAudioElement } = {};

  // WaveSurfer instances for each voice message
  waveSurfers: { [messageId: string]: WaveSurfer } = {};

  constructor(
    private modalService: ModalService,
    private workoutRecordService: WorkoutRecordService,
    private smartNoteService: SmartNoteService,
    private userService: UserService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
    private fileService: FileService,
    private toastService: ToastService
  ) {
  }

  // Grouped messages for Instagram-style display
  get groupedMessages(): MessageGroup[] {
    return this.groupMessagesBySender(this.smartNotes);
  }

  ngOnInit(): void {
    this.loadSmartNotes();
    this.loadCurrentUserId();
    // Auto-scroll to bottom after initial load
    setTimeout(() => this.scrollToBottom(), 200);
    // Preload waveforms after a short delay
    setTimeout(() => this.preloadAudioWaveforms(), 300);
  }

  isCurrentUser(smartNote: SmartNote): boolean {
    return smartNote.creatorId === this.currentUserId;
  }

  handleSetRecordClick(setRecord: WorkoutExerciseSetRecord) {
    this.selectedSetRecord = setRecord;
    this.loadSmartNotes();

    // Clear existing WaveSurfer instances for the previous set
    this.clearAllWaveSurfers();

    // Auto-scroll to bottom when switching sets
    setTimeout(() => this.scrollToBottom(), 100);

    // Preload waveforms for new set with longer delay to ensure DOM is ready
    setTimeout(() => this.preloadAudioWaveforms(), 500);
  }

  handleSendMessage(): void {
    if (!this.message?.trim() || this.isLoading) {
      return;
    }

    this.isLoading = true;

    const createRequest: SmartNoteCreateRequest = {
      type: SmartNoteType.TEXT,
      content: this.message.trim(),
      mediaId: '' // Empty for text messages
    };

    const workoutRecordId = this.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    this.workoutRecordService
      .createSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        createRequest
      )
      .subscribe({
        next: (smartNote) => {
          // Add the new smart note to the local array using functional approach
          this.smartNotes = [...this.smartNotes, smartNote];

          // Also update the selected set record's smart notes array
          this.selectedSetRecord.smartNotes = [...this.smartNotes];

          this.message = '';
          this.isLoading = false;

          // Auto-scroll to bottom
          this.scrollToBottom();
        },
        error: (error) => {
          console.error('Error creating smart note:', error);
          this.isLoading = false;
        }
      });
  }

  async handleStartRecording(): Promise<void> {
    if (this.isRecording) {
      this.stopRecording();
      return;
    }

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Create MediaRecorder instance
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: this.getSupportedMimeType()
      });

      this.audioChunks = [];
      this.recordingDuration = 0;
      this.recordingCancelled = false;
      this.isRecording = true;

      // Handle data available event
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      // Handle recording stop event
      this.mediaRecorder.onstop = () => {
        this.processRecording();
        stream.getTracks().forEach((track) => track.stop()); // Stop microphone access
      };

      // Start recording
      this.mediaRecorder.start(1000); // Collect data every second

      // Start timer
      this.startRecordingTimer();

      // Start real-time visualization
      this.startRealtimeVisualization(stream);

      // Scroll to bottom to ensure recording interface is visible
      setTimeout(() => this.scrollToBottom(), 100);
    } catch (error) {
      console.error('Error starting recording:', error);
      this.toastService.showInfoToast('smart-notes.recording-error');
    }
  }

  stopRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
      this.stopRealtimeVisualization();

      // Scroll to bottom when recording interface disappears
      setTimeout(() => this.scrollToBottom(), 100);
    }
  }

  cancelRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      // Set flag to prevent processing
      this.recordingCancelled = true;

      // Stop recording
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
      this.stopRealtimeVisualization();

      // Clear audio chunks to prevent upload
      this.audioChunks = [];
      this.recordingDuration = 0;

      // Stop microphone access
      const stream = this.mediaRecorder.stream;
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      this.toastService.showInfoToast('smart-notes.recording-cancelled');

      // Scroll to bottom when recording interface disappears
      setTimeout(() => this.scrollToBottom(), 100);
    }
  }

  sendRecording(): void {
    this.stopRecording(); // This will trigger processRecording via the onstop event
  }

  onInputFocus(): void {
    this.isInputFocused = true;
  }

  onInputBlur(): void {
    this.isInputFocused = false;
  }

  // Smart note creation method for the reusable chat component
  createSmartNote = (request: SmartNoteCreateRequest): Observable<SmartNote> => {
    const workoutRecordId = this.selectedSetRecord.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.selectedSetRecord.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    return this.workoutRecordService
      .createSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        request
      )
      .pipe(
        switchMap((smartNote) => {
          // Add to local array
          this.smartNotes = [...this.smartNotes, smartNote];
          this.selectedSetRecord.smartNotes = [...this.smartNotes];

          // Show success toast
          this.toastService.showInfoToast('smart-notes.upload-success');

          return of(smartNote);
        })
      );
  };

  // Media upload URL generator for the reusable chat component
  getMediaUploadUrl = (model: StorageObjectUploadUrlRequest): Observable<StorageObject> => {
    return this.smartNoteService.getMediaUploadUrl(environment.TRAINING_SERVICE_API_URL)(model);
  };

  // Delete note method for the reusable chat component
  deleteSmartNote = (noteId: string): Observable<void> => {
    const workoutRecordId = this.selectedSetRecord.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.selectedSetRecord.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    return this.workoutRecordService
      .deleteSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        noteId
      )
      .pipe(
        switchMap(() => {
          // Remove from local array using functional approach
          this.smartNotes = this.smartNotes.filter(note => note.id !== noteId);
          this.selectedSetRecord.smartNotes = [...this.smartNotes];
          return of(undefined);
        })
      );
  };

  // Photo click handler for the reusable chat component
  handlePhotoClick = (photoUrl: string): void => {
    // Here you would typically open a photo viewer modal
    // For now, we'll just open in a new tab
    window.open(photoUrl, '_blank');
  };

  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Voice message playback methods with WaveSurfer
  toggleVoicePlayback(messageId: string, audioUrl: string): void {
    const waveSurfer = this.waveSurfers[messageId];

    if (!waveSurfer) {
      // Stop all other playing audio first
      this.stopAllOtherAudio(messageId);

      // Determine if this is own message for color styling
      const message = this.smartNotes.find((note) => note.id === messageId);
      const isOwnMessage = message ? this.isCurrentUser(message) : false;

      // Create new WaveSurfer instance if it doesn't exist
      this.createWaveSurfer(messageId, audioUrl, isOwnMessage);
    } else {
      // Toggle existing WaveSurfer
      if (this.isVoicePlaying[messageId]) {
        waveSurfer.pause();
        this.isVoicePlaying[messageId] = false;
      } else {
        // Stop all other playing audio first
        this.stopAllOtherAudio(messageId);
        waveSurfer.play();
        this.isVoicePlaying[messageId] = true;
      }
    }
  }

  getVoiceDurationDisplay(messageId: string): string {
    const duration = this.voiceDuration[messageId];
    const currentTime = this.voiceCurrentTime[messageId] || 0;

    if (this.isVoicePlaying[messageId] && currentTime > 0) {
      return this.formatDuration(currentTime);
    } else if (duration && duration > 0) {
      return this.formatDuration(duration);
    } else {
      return '0:00';
    }
  }

  handlePhotoClick(photoUrl: string): void {
    this.modalService.showPhoto(photoUrl);
  }

  handleMessageHoldStart(messageId: string): void {
    this.deleteHoldTimer = setTimeout(() => {
      this.deleteButtonVisible[messageId] = true;
    }, 500); // Show delete button after 500ms hold
  }

  handleMessageHoldEnd(): void {
    if (this.deleteHoldTimer) {
      clearTimeout(this.deleteHoldTimer);
      this.deleteHoldTimer = null;
    }
  }

  handleDeleteMessage(messageId: string): void {
    this.smartNoteService
      .delete(environment.TRAINING_SERVICE_API_URL, messageId)
      .subscribe({
        next: () => {
          // Remove from local array using functional approach
          this.smartNotes = this.smartNotes.filter(
            (note) => note.id !== messageId
          );
          this.selectedSetRecord.smartNotes = [...this.smartNotes];

          // Hide delete button
          delete this.deleteButtonVisible[messageId];

          this.toastService.showInfoToast('smart-notes.delete-success');
        },
        error: (error) => {
          console.error('Error deleting smart note:', error);
          this.toastService.showInfoToast('smart-notes.delete-error');
        }
      });
  }

  hideDeleteButton(messageId: string): void {
    delete this.deleteButtonVisible[messageId];
  }

  triggerFileSelect(): void {
    this.fileInput.nativeElement.click();
  }

  handleFileSelect(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Determine smart note type based on file type
    const isVideo = file.type.startsWith('video/');
    const smartNoteType = isVideo ? SmartNoteType.VIDEO : SmartNoteType.PHOTO;

    // Start upload with progress tracking
    this.fileService
      .uploadFile(
        event,
        this.smartNoteService.getMediaUploadUrl(
          environment.TRAINING_SERVICE_API_URL
        ),
        environment.TRAINING_SERVICE_API_URL
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Show upload progress
            this.uploadProgress = storageObject;
            // Scroll to bottom after progress is shown
            setTimeout(() => this.scrollToBottom(), 100);
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: smartNoteType,
            content: '', // Empty for media messages
            mediaId: storageObject.id
          };

          const workoutRecordId = this.exerciseRecord.workoutRecord.id;
          const exerciseRecordId = this.exerciseRecord.id;
          const setRecordId = this.selectedSetRecord.id;

          return this.workoutRecordService
            .createSetRecordSmartNote(
              workoutRecordId,
              exerciseRecordId,
              setRecordId,
              createRequest
            )
            .pipe(
              switchMap((smartNote) => {
                // Clear upload progress
                this.uploadProgress = null;

                // Add to local array
                this.smartNotes = [...this.smartNotes, smartNote];
                this.selectedSetRecord.smartNotes = [...this.smartNotes];

                // Show success toast
                this.toastService.showInfoToast('smart-notes.upload-success');

                // Clear file input
                this.fileInput.nativeElement.value = '';

                // Auto-scroll to bottom after DOM update
                setTimeout(() => {
                  this.scrollToBottom();
                }, 200);

                return of(smartNote);
              })
            );
        })
      )
      .subscribe({
        error: (error) => {
          console.error('Error uploading file:', error);
          this.uploadProgress = null;
          this.fileInput.nativeElement.value = '';
          this.toastService.showInfoToast('smart-notes.upload-error');
        }
      });
  }

  // Method to handle S3 image loading with retry logic
  handleImageLoad(event: any, imageUrl: string): void {
    const img = event.target as HTMLImageElement;

    // If image failed to load (404), retry after a delay
    if (img.naturalWidth === 0) {
      this.retryImageLoad(img, imageUrl, 0);
    }
  }

  handleImageError(event: any, imageUrl: string): void {
    const img = event.target as HTMLImageElement;
    this.retryImageLoad(img, imageUrl, 0);
  }

  ngOnDestroy(): void {
    // Clean up all WaveSurfer instances
    this.clearAllWaveSurfers();

    // Clear recording timer if active
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    // Stop any ongoing recording
    if (this.isRecording && this.mediaRecorder) {
      this.mediaRecorder.stop();
    }

    // Stop real-time visualization
    this.stopRealtimeVisualization();
  }

  private startRealtimeVisualization(stream: MediaStream): void {
    try {
      this.audioContext = new AudioContext();
      this.analyser = this.audioContext.createAnalyser();

      const source = this.audioContext.createMediaStreamSource(stream);
      source.connect(this.analyser);

      this.analyser.fftSize = 64;
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      this.animateWaveform();
    } catch (error) {
      console.error('Error setting up audio visualization:', error);
    }
  }

  private animateWaveform(): void {
    if (!this.analyser || !this.dataArray || !this.isRecording) {
      return;
    }

    this.analyser.getByteFrequencyData(this.dataArray);

    // Update sound bars based on frequency data
    for (let i = 0; i < this.realtimeSoundBars.length; i++) {
      const dataIndex = Math.floor((i / this.realtimeSoundBars.length) * this.dataArray.length);
      const amplitude = this.dataArray[dataIndex] || 0;

      // Convert amplitude (0-255) to bar height (2-40px)
      const barHeight = Math.max(2, Math.floor((amplitude / 255) * 38) + 2);
      this.realtimeSoundBars[i] = barHeight;
    }

    // Continue animation
    this.animationFrame = requestAnimationFrame(() => this.animateWaveform());
  }

  private stopRealtimeVisualization(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.analyser = null;
    this.dataArray = null;

    // Reset bars to minimum height
    this.realtimeSoundBars = Array(20).fill(2);
  }

  private stopAllOtherAudio(currentMessageId: string): void {
    Object.keys(this.waveSurfers).forEach(messageId => {
      if (messageId !== currentMessageId && this.isVoicePlaying[messageId]) {
        const waveSurfer = this.waveSurfers[messageId];
        if (waveSurfer) {
          waveSurfer.pause();
          this.isVoicePlaying[messageId] = false;
        }
      }
    });
  }

  private preloadAudioWaveforms(): void {
    this.smartNotes
      .filter((note) => note.type === SmartNoteType.AUDIO && note.mediaUrl)
      .forEach((audioNote) => {
        if (!this.waveSurfers[audioNote.id]) {
          const isOwnMessage = this.isCurrentUser(audioNote);
          // Delay creation to ensure DOM elements are rendered
          setTimeout(() => {
            this.createWaveSurfer(audioNote.id, audioNote.mediaUrl, isOwnMessage);
          }, 300);
        }
      });
  }

  private clearAllWaveSurfers(): void {
    // Destroy all existing WaveSurfer instances
    Object.keys(this.waveSurfers).forEach(messageId => {
      const waveSurfer = this.waveSurfers[messageId];
      if (waveSurfer) {
        try {
          waveSurfer.destroy();
        } catch (error) {
          console.warn('Error destroying WaveSurfer instance:', error);
        }
      }
    });

    // Clear the instances object
    this.waveSurfers = {};

    // Reset playback states
    this.isVoicePlaying = {};
    this.voiceProgress = {};
    this.voiceDuration = {};
    this.voiceCurrentTime = {};
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // Fallback
  }

  private startRecordingTimer(): void {
    this.recordingTimer = setInterval(() => {
      this.recordingDuration++;

      // Auto-stop at max duration
      if (this.recordingDuration >= this.maxRecordingDuration) {
        this.stopRecording();
        this.toastService.showInfoToast('smart-notes.recording-max-duration');
      }
    }, 1000);
  }

  private clearRecordingTimer(): void {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }

  private processRecording(): void {
    if (this.recordingCancelled || this.audioChunks.length === 0) {
      // Recording was cancelled or empty, reset state and don't process
      this.recordingCancelled = false;
      this.audioChunks = [];
      this.recordingDuration = 0;
      return;
    }

    // Create audio blob
    const audioBlob = new Blob(this.audioChunks, {
      type: this.getSupportedMimeType()
    });

    // Upload the audio file
    this.uploadAudioFile(audioBlob);
  }

  private uploadAudioFile(audioBlob: Blob): void {
    // Create a File object from the blob
    const audioFile = new File(
      [audioBlob],
      `voice-message-${Date.now()}.webm`,
      {
        type: audioBlob.type
      }
    );

    // Create a fake file input event
    const fakeEvent = {
      target: {
        files: [audioFile]
      }
    };

    // Start upload with progress tracking
    this.fileService
      .uploadFile(
        fakeEvent,
        this.smartNoteService.getMediaUploadUrl(
          environment.TRAINING_SERVICE_API_URL
        ),
        environment.TRAINING_SERVICE_API_URL
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Show upload progress
            this.uploadProgress = storageObject;
            setTimeout(() => this.scrollToBottom(), 100);
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: SmartNoteType.AUDIO,
            content: `Voice message (${this.formatDuration(
              this.recordingDuration
            )})`,
            mediaId: storageObject.id
          };

          const workoutRecordId = this.exerciseRecord.workoutRecord.id;
          const exerciseRecordId = this.exerciseRecord.id;
          const setRecordId = this.selectedSetRecord.id;

          return this.workoutRecordService
            .createSetRecordSmartNote(
              workoutRecordId,
              exerciseRecordId,
              setRecordId,
              createRequest
            )
            .pipe(
              switchMap((smartNote) => {
                // Clear upload progress
                this.uploadProgress = null;

                // Add to local array
                this.smartNotes = [...this.smartNotes, smartNote];
                this.selectedSetRecord.smartNotes = [...this.smartNotes];

                // Auto-scroll to bottom after DOM update
                setTimeout(() => {
                  this.scrollToBottom();
                }, 200);

                // Create WaveSurfer for the new voice message
                setTimeout(() => {
                  const isOwnMessage = this.isCurrentUser(smartNote);
                  this.createWaveSurfer(smartNote.id, smartNote.mediaUrl, isOwnMessage);
                }, 500);

                return of(smartNote);
              })
            );
        })
      )
      .subscribe({
        error: (error) => {
          console.error('Error uploading voice message:', error);
          this.uploadProgress = null;
          this.toastService.showInfoToast('smart-notes.voice-upload-error');
        }
      });
  }

  private createWaveSurfer(
    messageId: string,
    audioUrl: string,
    isOwnMessage: boolean = false
  ): void {
    const container = document.getElementById(`waveform-${messageId}`);
    if (!container) {
      console.warn('Waveform container not found for message:', messageId, 'retrying...');
      // Retry after a longer delay if container doesn't exist
      setTimeout(() => {
        this.createWaveSurfer(messageId, audioUrl, isOwnMessage);
      }, 500);
      return;
    }

    // Check if WaveSurfer already exists for this message
    if (this.waveSurfers[messageId]) {
      console.warn('WaveSurfer already exists for message:', messageId);
      return;
    }

    // Instagram-style colors: black-400 for unplayed, black-600 for played
    const waveColor = isOwnMessage
      ? '#9CA3AF' // gray-400
      : 'rgba(255, 255, 255, 0.4)';
    const progressColor = isOwnMessage
      ? '#4B5563' // gray-600
      : 'rgba(255, 255, 255, 0.8)';

    const waveSurfer = WaveSurfer.create({
      container: container,
      waveColor: waveColor,
      progressColor: progressColor,
      cursorColor: 'transparent',
      barWidth: 3,
      barGap: 2,
      barRadius: 2,
      height: 32,
      normalize: true,
      interact: true,
      autoCenter: false,
      url: audioUrl
    });

    // Store the instance
    this.waveSurfers[messageId] = waveSurfer;

    // Event listeners
    waveSurfer.on('ready', () => {
      const duration = waveSurfer.getDuration();
      this.voiceDuration[messageId] = Math.floor(duration);
      // Don't auto-play, just show the waveform ready for interaction
    });

    waveSurfer.on('audioprocess', () => {
      const currentTime = waveSurfer.getCurrentTime();
      const duration = waveSurfer.getDuration();
      this.voiceCurrentTime[messageId] = Math.floor(currentTime);
      this.voiceProgress[messageId] = (currentTime / duration) * 100;
    });

    waveSurfer.on('play', () => {
      this.isVoicePlaying[messageId] = true;
    });

    waveSurfer.on('pause', () => {
      this.isVoicePlaying[messageId] = false;
    });

    waveSurfer.on('finish', () => {
      this.isVoicePlaying[messageId] = false;
      this.voiceProgress[messageId] = 0;
      this.voiceCurrentTime[messageId] = 0;
      waveSurfer.seekTo(0);
    });

    waveSurfer.on('error', (error) => {
      console.error('WaveSurfer error for message', messageId, error);
      this.isVoicePlaying[messageId] = false;
    });
  }

  private loadCurrentUserId(): void {
    this.userService.loggedUserId$.subscribe((userId) => {
      this.currentUserId = userId;
    });
  }

  private loadSmartNotes(): void {
    if (!this.selectedSetRecord) {
      this.selectedSetRecord = this.exerciseRecord.setRecords[0];
    }

    // Use functional approach to ensure immutability
    this.smartNotes = this.selectedSetRecord.smartNotes
      ? [...this.selectedSetRecord.smartNotes]
      : [];
  }

  private groupMessagesBySender(messages: SmartNote[]): MessageGroup[] {
    if (!messages.length) return [];

    const groups: MessageGroup[] = [];
    let currentGroup: MessageGroup | null = null;

    // Sort messages by creation time
    const sortedMessages = [...messages].sort(
      (a, b) =>
        new Date(a.createdOn).getTime() - new Date(b.createdOn).getTime()
    );

    sortedMessages.forEach((message) => {
      const isCurrentUser = this.isCurrentUser(message);
      const messageTime = new Date(message.createdOn);

      // Check if we should start a new group
      const shouldStartNewGroup =
        !currentGroup ||
        currentGroup.creatorId !== message.creatorId ||
        this.shouldBreakGroup(currentGroup.timestamp, message.createdOn);

      if (shouldStartNewGroup) {
        // Start new group
        currentGroup = {
          creatorId: message.creatorId,
          creatorName: message.creatorName,
          timestamp: message.createdOn,
          isCurrentUser,
          messages: [message]
        };
        groups.push(currentGroup);
      } else {
        // Add to existing group
        currentGroup.messages.push(message);
      }
    });

    return groups;
  }

  private shouldBreakGroup(
    lastTimestamp: string,
    currentTimestamp: string
  ): boolean {
    const lastTime = new Date(lastTimestamp);
    const currentTime = new Date(currentTimestamp);
    const timeDiff = currentTime.getTime() - lastTime.getTime();

    // Break group if more than 5 minutes apart
    return timeDiff > 5 * 60 * 1000;
  }

  private scrollToBottom(): void {
    if (this.messagesArea?.nativeElement) {
      const element = this.messagesArea.nativeElement;

      // Force immediate scroll
      element.scrollTop = element.scrollHeight;

      // Also try with a small delay to ensure DOM is fully updated
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
      }, 50);

      // Final attempt with longer delay for complex content
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
      }, 150);
    }
  }

  private retryImageLoad(
    img: HTMLImageElement,
    imageUrl: string,
    attempt: number
  ): void {
    const maxRetries = 5;
    const retryDelay = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s

    if (attempt < maxRetries) {
      setTimeout(() => {
        // Force reload by adding timestamp
        const urlWithTimestamp = `${imageUrl}?t=${Date.now()}`;
        img.src = urlWithTimestamp;

        // Check if image loaded after a short delay
        setTimeout(() => {
          if (img.naturalWidth === 0) {
            this.retryImageLoad(img, imageUrl, attempt + 1);
          }
        }, 500);
      }, retryDelay);
    }
  }
}
