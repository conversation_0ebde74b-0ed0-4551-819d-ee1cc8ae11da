.set-selector {
  margin-top: 16px;

  ion-label {
    color: var(--color-background-primary-black-300);
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    width: 100%;
    cursor: pointer;

    &.selected {
      color: var(--color-background-secondary-white-700);
    }
  }
}

.messages-container {
  margin-top: 16px;
  margin-bottom: 16px;

  .messages-area {
    height: calc(100vh - 230px); // Default height for normal input
    overflow-y: auto;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    transition: height 0.3s ease; // Smooth transition

    // Dynamic height when recording
    &.recording-mode {
      height: calc(100vh - 350px); // Extra space for recording interface
    }

    // Hide scrollbar but keep functionality
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE and Edge

    &::-webkit-scrollbar {
      display: none; // Chrome, Safari, Opera
    }

    .message-group {
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      &.own-group {
        align-items: flex-end;

        .group-header {
          text-align: right;
        }
      }

      .group-header {
        margin-bottom: 8px;

        .sender-name {
          font-size: 14px;
          font-weight: 300;
          color: var(--color-background-secondary-white-900);
          margin-right: 8px;
        }

        .group-time {
          font-size: 12px;
          color: var(--color-background-primary-black-300);
          font-weight: 300;
        }
      }

      .message {
        margin-bottom: 2px; // Small gap between messages in group
        max-width: 80%;

        &:last-child {
          margin-bottom: 0;
        }

        .message-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          gap: 8px;

          .message-content {
            background: var(--color-background-primary-black-400);
            color: var(--color-background-secondary-white-600);
            padding: 12px 16px;
            word-wrap: break-word;
            border-radius: var(--radius-medium);

            // Remove padding for media messages (photos, videos, voice)
            &:has(.voice-message),
            &:has(.message-media) {
              padding: 0 !important;
            }

            flex: 1;

            .message-text {
              font-size: 16px;
              line-height: 1.4;
            }

            .message-media {
              width: 100%;
              max-width: 100%;
              max-height: 300px;
              border-radius: 8px;
              object-fit: cover;
              display: block;
              margin: 0;
              padding: 0;
              vertical-align: top;

              &.clickable {
                cursor: pointer;
                transition: opacity 0.2s ease;

                &:hover {
                  opacity: 0.9;
                }

                &:active {
                  opacity: 0.8;
                }
              }
            }

            // Instagram-style voice message
            .voice-message {
              display: flex !important;
              align-items: center !important;
              gap: 12px !important;
              min-width: 200px;
              padding: 8px 12px;

              .voice-play-btn {
                --padding-start: 6px;
                --padding-end: 6px;
                --padding-top: 6px;
                --padding-bottom: 6px;
                width: 32px !important;
                height: 32px !important;
                border-radius: 50%;
                --background: var(--color-background-secondary-white-600);
                --color: var(--color-background-primary-black-800);
                flex-shrink: 0 !important;

                ion-icon {
                  font-size: 16px;
                }
              }

              .voice-waveform {
                flex: 1 !important;
                height: 32px !important;
                width: 100% !important;
                cursor: pointer;
                min-width: 100px;
                max-width: 100%;
                overflow: hidden;
                position: relative;

                // WaveSurfer canvas styling
                ::ng-deep canvas {
                  width: 100% !important;
                  height: 32px !important;
                  max-width: 100% !important;
                  display: block !important;
                }

                // Hide WaveSurfer's default controls
                ::ng-deep .wavesurfer-region {
                  display: none;
                }

                // Prevent WaveSurfer from resizing
                ::ng-deep .wavesurfer-wrapper {
                  width: 100% !important;
                  height: 32px !important;
                  overflow: hidden;
                }
              }

              .voice-time {
                font-size: 13px !important;
                color: var(--color-background-secondary-white-600);
                min-width: 30px !important;
                text-align: right;
                flex-shrink: 0 !important;
                font-weight: 500;
              }
            }
          }

          .delete-button-container {
            display: flex;
            flex-direction: column;
            gap: 4px;
            opacity: 0;
            animation: fadeIn 0.2s ease forwards;

            .delete-button, .cancel-button {
              --padding-start: 8px;
              --padding-end: 8px;
              --padding-top: 8px;
              --padding-bottom: 8px;
              width: 32px;
              height: 32px;
              border-radius: 50%;

              ion-icon {
                font-size: 16px;
              }
            }

            .delete-button {
              --background: var(--color-danger);
              --color: white;
            }

            .cancel-button {
              --background: var(--color-background-primary-black-400);
              --color: var(--color-background-secondary-white-600);
            }
          }
        }

        // Instagram-style border radius adjustments
        &.single-in-group .message-content {
          border-radius: var(--radius-medium);
        }

        &.first-in-group:not(.last-in-group) .message-content {
          border-radius: var(--radius-large, 24px) var(--radius-large, 24px) var(--radius-large, 24px) var(--radius-small, 6px);
        }

        &.last-in-group:not(.first-in-group) .message-content {
          border-radius: var(--radius-small, 6px) var(--radius-medium, 16px) var(--radius-medium, 16px) var(--radius-medium, 16px);
        }

        &:not(.first-in-group):not(.last-in-group) .message-content {
          border-radius: var(--radius-small, 6px) var(--radius-medium, 16px) var(--radius-medium, 16px) var(--radius-small, 6px);
        }
      }

      // Adjust border radius for own messages (right side)
      &.own-group {
        .message {
          .message-wrapper {
            flex-direction: row-reverse; // Put delete buttons on left for own messages

            .message-content {
              background: var(--color-background-secondary-white-700, #F2F2F2) !important;
              color: var(--color-background-primary-black-800, #080808) !important;

              // Voice message styling for own messages
              .voice-message {
                .voice-play-btn {
                  --background: var(--color-background-primary-black-800) !important;
                  --color: var(--color-background-secondary-white-700) !important;
                }

                .voice-time {
                  color: var(--color-background-primary-black-800) !important;
                }
              }
            }
          }

          &.first-in-group:not(.last-in-group) .message-content {
            border-radius: var(--radius-large, 24px) var(--radius-large, 24px) var(--radius-small, 6px) var(--radius-large, 24px);
          }

          &.last-in-group:not(.first-in-group) .message-content {
            border-radius: var(--radius-medium, 16px) var(--radius-small, 6px) var(--radius-medium, 16px) var(--radius-medium, 16px);
          }

          &:not(.first-in-group):not(.last-in-group) .message-content {
            border-radius: var(--radius-medium, 16px) var(--radius-small, 6px) var(--radius-small, 6px) var(--radius-medium, 16px);
          }
        }
      }
    }

    .empty-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-background-primary-black-300);
      font-style: italic;
    }

    .upload-progress-container {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;
      padding: 0 24px;

      .upload-progress-card {
        background: var(--color-background-primary-black-300);
        color: var(--color-background-secondary-white-700);
        border-radius: var(--radius-medium);
        padding: 12px 16px;
        max-width: 80%;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .upload-info {
          display: flex;
          align-items: center;
          gap: 8px;

          ion-icon {
            font-size: 18px;
          }

          .upload-text {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .progress-bar {
          width: 100%;
          height: 4px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 2px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: var(--color-background-secondary-white-700);
            border-radius: 2px;
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          font-size: 12px;
          text-align: center;
          opacity: 0.9;
        }
      }
    }

  }
}

.chat-input {
  width: 100%;

  .chat-controls {
    background: var(--color-background-primary-black-400);
    border-radius: var(--radius-medium);
    padding: 0 8px;
  }

  ion-input {
    color: var(--color-background-secondary-white-600);
    --highlight-color-focused: none;
    --highlight-color-valid: none;
    --highlight-color-invalid: none;
    --padding-start: 8px !important;
  }

  .attach {
    background: var(--color-background-secondary-white-700);
    border-radius: var(--radius-small);
    padding: 5px;
    cursor: pointer;

    ion-icon {
      color: var(--color-background-primary-black-900);
      font-size: 20px;
    }
  }

  .record {
    font-size: 30px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;

    &.recording {
      color: var(--color-accent-primary-red-900, #B91C1C);
      animation: pulse 1s infinite;
    }
  }

  // Beautiful recording interface (replaces input)
  .recording-interface {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(220, 38, 38, 0.03));
    border-radius: 20px;
    border: 1px solid rgba(239, 68, 68, 0.15);
    animation: recordingPulse 2s ease-in-out infinite;

    .recording-status {
      display: flex;
      align-items: center;
      gap: 12px;

      .recording-timer {
        font-size: 18px;
        font-weight: 600;
        color: var(--color-accent-primary-red-600, #DC2626);
        min-width: 50px;
        text-align: center;
        font-family: 'Courier New', monospace;
      }

      .recording-visualizer {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 0 12px;

        .sound-waves {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 2px;
          height: 100%;

          .sound-bar {
            width: 3px;
            background: linear-gradient(to top,
              var(--color-accent-primary-red-500, #EF4444),
              var(--color-accent-primary-red-400, #F87171)
            );
            border-radius: 2px;
            min-height: 2px;
            max-height: 28px;
            transition: height 0.1s ease;
            animation: soundBarPulse 0.5s ease-in-out infinite alternate;
          }
        }
      }
    }

    .recording-actions {
      display: flex;
      justify-content: center;
      gap: 24px;

      .discard-btn {
        --padding-start: 16px;
        --padding-end: 16px;
        --padding-top: 16px;
        --padding-bottom: 16px;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        --background: transparent;
        --color: var(--color-accent-primary-red-600, #DC2626);
        transition: all 0.2s ease;
        border: 2px solid var(--color-accent-primary-red-600, #DC2626);

        &:hover {
          --background: var(--color-accent-primary-red-600, #DC2626);
          --color: white;
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        ion-icon {
          font-size: 28px;
          font-weight: bold;
        }
      }

      .done-btn {
        --padding-start: 16px;
        --padding-end: 16px;
        --padding-top: 16px;
        --padding-bottom: 16px;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        --background: transparent;
        --color: var(--color-accent-primary-green-600, #059669);
        transition: all 0.2s ease;
        border: 2px solid var(--color-accent-primary-green-600, #059669);

        &:hover {
          --background: var(--color-accent-primary-green-600, #059669);
          --color: white;
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        ion-icon {
          font-size: 28px;
          font-weight: bold;
        }
      }
    }
  }

  .send {
    font-size: 25px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
  }

  ion-spinner {
    --color: var(--color-background-secondary-white-600);
    width: 24px;
    height: 24px;
  }
}

// Animation for delete button appearance
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Animation for recording pulse
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Animation for recording interface
@keyframes recordingPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

// Animation for sound bars
@keyframes soundBarPulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
