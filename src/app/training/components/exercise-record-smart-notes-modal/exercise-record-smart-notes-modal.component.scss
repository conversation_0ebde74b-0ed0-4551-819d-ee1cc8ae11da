.set-selector {
  margin-top: 16px;

  ion-label {
    color: var(--color-background-primary-black-300);
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    width: 100%;
    cursor: pointer;

    &.selected {
      color: var(--color-background-secondary-white-700);
    }
  }
}

.chat-input {
  width: 90%;

  .chat-controls {
    background: var(--color-background-primary-black-400);
    border-radius: var(--radius-medium);
    padding: 0 8px;
  }

  ion-input {
    color: var(--color-background-secondary-white-600);
    --highlight-color-focused: none;
    --highlight-color-valid: none;
    --highlight-color-invalid: none;
    --padding-start: 8px !important;
  }

  .attach {
    background: var(--color-background-secondary-white-700);
    border-radius: var(--radius-small);
    padding: 5px;

    ion-icon {
      color: var(--color-background-primary-black-900);
      font-size: 20px;
    }

  }

  .record {
    font-size: 30px;
    color: var(--color-background-secondary-white-600);
  }

  .send {
    font-size: 25px;
    color: var(--color-background-secondary-white-600);
  }
}
