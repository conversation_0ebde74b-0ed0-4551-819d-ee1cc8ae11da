.set-selector {
  margin-top: 16px;

  ion-label {
    color: var(--color-background-primary-black-300);
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    width: 100%;
    cursor: pointer;

    &.selected {
      color: var(--color-background-secondary-white-700);
    }
  }
}

.messages-container {
  margin-top: 16px;
  margin-bottom: 16px;

  .messages-area {
    height: calc(100vh - 64px);
    overflow-y: auto;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .message {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      &.own-message {
        align-items: flex-end;

        .message-content {
          background: var(--color-accent-primary-red-800);
          color: var(--color-background-secondary-white-700);
        }
      }

      .message-content {
        background: var(--color-background-primary-black-400);
        color: var(--color-background-secondary-white-600);
        border-radius: var(--radius-medium);
        padding: 12px 16px;
        max-width: 80%;
        word-wrap: break-word;

        .message-text {
          font-size: 16px;
          line-height: 1.4;
          margin-bottom: 4px;
        }

        .message-time {
          font-size: 12px;
          opacity: 0.7;
        }
      }
    }

  }
}

.chat-input {
  width: 100%;
  padding: 16px 24px;

  .chat-controls {
    background: var(--color-background-primary-black-400);
    border-radius: var(--radius-medium);
    padding: 0 8px;
  }

  ion-input {
    color: var(--color-background-secondary-white-600);
    --highlight-color-focused: none;
    --highlight-color-valid: none;
    --highlight-color-invalid: none;
    --padding-start: 8px !important;
  }

  .attach {
    background: var(--color-background-secondary-white-700);
    border-radius: var(--radius-small);
    padding: 5px;
    cursor: pointer;

    ion-icon {
      color: var(--color-background-primary-black-900);
      font-size: 20px;
    }
  }

  .record {
    font-size: 30px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
  }

  .send {
    font-size: 25px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
  }

  ion-spinner {
    --color: var(--color-background-secondary-white-600);
    width: 24px;
    height: 24px;
  }
}
