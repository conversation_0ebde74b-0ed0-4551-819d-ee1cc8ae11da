.set-selector {
  margin-top: 16px;

  ion-label {
    color: var(--color-background-primary-black-300);
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    width: 100%;
    cursor: pointer;

    &.selected {
      color: var(--color-background-secondary-white-700);
    }
  }
}

.messages-container {
  margin-top: 16px;
  margin-bottom: 16px;

  .messages-area {
    height: calc(100vh - 230px);
    overflow-y: auto;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    // Hide scrollbar but keep functionality
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE and Edge

    &::-webkit-scrollbar {
      display: none; // Chrome, Safari, Opera
    }

    .message-group {
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      &.own-group {
        align-items: flex-end;

        .group-header {
          text-align: right;
        }
      }

      .group-header {
        margin-bottom: 8px;

        .sender-name {
          font-size: 14px;
          font-weight: 300;
          color: var(--color-background-secondary-white-900);
          margin-right: 8px;
        }

        .group-time {
          font-size: 12px;
          color: var(--color-background-primary-black-300);
          font-weight: 300;
        }
      }

      .message {
        margin-bottom: 2px; // Small gap between messages in group
        max-width: 80%;

        &:last-child {
          margin-bottom: 0;
        }

        .message-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          gap: 8px;

          .message-content {
            background: var(--color-background-primary-black-400);
            color: var(--color-background-secondary-white-600);
            padding: 12px 16px;
            word-wrap: break-word;
            border-radius: var(--radius-medium);
            flex: 1;

            .message-text {
              font-size: 16px;
              line-height: 1.4;
            }

            .message-media {
              max-width: 100%;
              max-height: 300px;
              border-radius: 8px;
              object-fit: cover;

              &.clickable {
                cursor: pointer;
                transition: opacity 0.2s ease;

                &:hover {
                  opacity: 0.9;
                }

                &:active {
                  opacity: 0.8;
                }
              }
            }
          }

          .delete-button-container {
            display: flex;
            flex-direction: column;
            gap: 4px;
            opacity: 0;
            animation: fadeIn 0.2s ease forwards;

            .delete-button, .cancel-button {
              --padding-start: 8px;
              --padding-end: 8px;
              --padding-top: 8px;
              --padding-bottom: 8px;
              width: 32px;
              height: 32px;
              border-radius: 50%;

              ion-icon {
                font-size: 16px;
              }
            }

            .delete-button {
              --background: var(--color-danger);
              --color: white;
            }

            .cancel-button {
              --background: var(--color-background-primary-black-400);
              --color: var(--color-background-secondary-white-600);
            }
          }
        }

        // Instagram-style border radius adjustments
        &.single-in-group .message-content {
          border-radius: var(--radius-medium);
        }

        &.first-in-group:not(.last-in-group) .message-content {
          border-radius: var(--radius-large, 24px) var(--radius-large, 24px) var(--radius-large, 24px) var(--radius-small, 6px);
        }

        &.last-in-group:not(.first-in-group) .message-content {
          border-radius: var(--radius-small, 6px) var(--radius-medium, 16px) var(--radius-medium, 16px) var(--radius-medium, 16px);
        }

        &:not(.first-in-group):not(.last-in-group) .message-content {
          border-radius: var(--radius-small, 6px) var(--radius-medium, 16px) var(--radius-medium, 16px) var(--radius-small, 6px);
        }
      }

      // Adjust border radius for own messages (right side)
      &.own-group {
        .message {
          .message-wrapper {
            flex-direction: row-reverse; // Put delete buttons on left for own messages

            .message-content {
              background: var(--color-accent-primary-red-800) !important;
              color: var(--color-background-secondary-white-700) !important;
            }
          }

          &.first-in-group:not(.last-in-group) .message-content {
            border-radius: var(--radius-large, 24px) var(--radius-large, 24px) var(--radius-small, 6px) var(--radius-large, 24px);
          }

          &.last-in-group:not(.first-in-group) .message-content {
            border-radius: var(--radius-medium, 16px) var(--radius-small, 6px) var(--radius-medium, 16px) var(--radius-medium, 16px);
          }

          &:not(.first-in-group):not(.last-in-group) .message-content {
            border-radius: var(--radius-medium, 16px) var(--radius-small, 6px) var(--radius-small, 6px) var(--radius-medium, 16px);
          }
        }
      }
    }

    .empty-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-background-primary-black-300);
      font-style: italic;
    }

    .upload-progress-container {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;
      padding: 0 24px;

      .upload-progress-card {
        background: var(--color-accent-primary-red-800);
        color: var(--color-background-secondary-white-700);
        border-radius: var(--radius-medium);
        padding: 12px 16px;
        max-width: 80%;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .upload-info {
          display: flex;
          align-items: center;
          gap: 8px;

          ion-icon {
            font-size: 18px;
          }

          .upload-text {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .progress-bar {
          width: 100%;
          height: 4px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 2px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: var(--color-background-secondary-white-700);
            border-radius: 2px;
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          font-size: 12px;
          text-align: center;
          opacity: 0.9;
        }
      }
    }

  }
}

.chat-input {
  width: 100%;

  .chat-controls {
    background: var(--color-background-primary-black-400);
    border-radius: var(--radius-medium);
    padding: 0 8px;
  }

  ion-input {
    color: var(--color-background-secondary-white-600);
    --highlight-color-focused: none;
    --highlight-color-valid: none;
    --highlight-color-invalid: none;
    --padding-start: 8px !important;
  }

  .attach {
    background: var(--color-background-secondary-white-700);
    border-radius: var(--radius-small);
    padding: 5px;
    cursor: pointer;

    ion-icon {
      color: var(--color-background-primary-black-900);
      font-size: 20px;
    }
  }

  .record {
    font-size: 30px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
  }

  .send {
    font-size: 25px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
  }

  ion-spinner {
    --color: var(--color-background-secondary-white-600);
    width: 24px;
    height: 24px;
  }
}

// Animation for delete button appearance
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
