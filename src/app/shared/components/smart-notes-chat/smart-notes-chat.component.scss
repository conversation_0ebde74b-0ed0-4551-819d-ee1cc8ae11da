.smart-notes-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .messages-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .messages-area {
    height: calc(100vh - 230px - env(safe-area-inset-bottom)); // Default height for normal input
    overflow-y: auto;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    transition: height 0.3s ease; // Smooth transition

    // Dynamic height when recording
    &.recording-mode {
      height: calc(100vh - 350px - env(safe-area-inset-bottom)); // Extra space for recording interface
    }

    // Hide scrollbar but keep functionality
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE and Edge

    &::-webkit-scrollbar {
      display: none; // Chrome, Safari, Opera
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--color-background-secondary-white-600);
    text-align: center;

    ion-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    p {
      font-size: 16px;
      margin: 0;
      opacity: 0.8;
    }
  }

  .message {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;

    &.own-message {
      align-items: flex-end;

      .message-content {
        background: var(--color-background-secondary-white-700, #F2F2F2) !important;
        color: var(--color-background-primary-black-800, #080808) !important;
        border-radius: var(--radius-medium) var(--radius-small) var(--radius-medium) var(--radius-medium);

        // Voice message styling for own messages
        .voice-message {
          .voice-play-btn {
            --background: var(--color-background-primary-black-800) !important;
            --color: var(--color-background-secondary-white-700) !important;
          }
          
          .voice-time {
            color: var(--color-background-primary-black-800) !important;
          }
        }
      }

      .message-info {
        align-items: flex-end;
      }
    }

    &:not(.own-message) {
      align-items: flex-start;

      .message-content {
        background: var(--color-background-primary-black-400);
        color: var(--color-background-secondary-white-600);
        border-radius: var(--radius-small) var(--radius-medium) var(--radius-medium) var(--radius-medium);
      }

      .message-info {
        align-items: flex-start;
      }
    }

    .message-content {
      max-width: 80%;
      padding: 12px 16px;
      word-wrap: break-word;
      border-radius: var(--radius-medium);

      // Remove padding for media messages (photos, videos, voice)
      &:has(.voice-message),
      &:has(.message-media) {
        padding: 0 !important;
      }

      .message-text {
        margin: 0;
        line-height: 1.4;
      }

      .message-media {
        width: 100%;
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        object-fit: cover;
        display: block;
        margin: 0;
        padding: 0;
        vertical-align: top;

        &.clickable {
          cursor: pointer;
          transition: opacity 0.2s ease;

          &:hover {
            opacity: 0.9;
          }

          &:active {
            opacity: 0.8;
          }
        }
      }

      // Instagram-style voice message
      .voice-message {
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        min-width: 200px;
        padding: 8px 12px;

        .voice-play-btn {
          --padding-start: 6px;
          --padding-end: 6px;
          --padding-top: 6px;
          --padding-bottom: 6px;
          width: 32px !important;
          height: 32px !important;
          border-radius: 50%;
          --background: var(--color-background-secondary-white-600);
          --color: var(--color-background-primary-black-800);
          flex-shrink: 0 !important;

          ion-icon {
            font-size: 16px;
          }
        }

        .voice-waveform {
          flex: 1 !important;
          height: 32px !important;
          width: 100% !important;
          cursor: pointer;
          min-width: 100px;
          max-width: 100%;
          overflow: hidden;
          position: relative;

          // WaveSurfer canvas styling
          ::ng-deep canvas {
            width: 100% !important;
            height: 32px !important;
            max-width: 100% !important;
            display: block !important;
          }

          // Hide WaveSurfer's default controls
          ::ng-deep .wavesurfer-region {
            display: none;
          }

          // Prevent WaveSurfer from resizing
          ::ng-deep .wavesurfer-wrapper {
            width: 100% !important;
            height: 32px !important;
            overflow: hidden;
          }
        }

        .voice-time {
          font-size: 11px !important;
          color: var(--color-background-secondary-white-600);
          min-width: 30px !important;
          text-align: right;
          flex-shrink: 0 !important;
          font-weight: 500;
        }
      }
    }

    .message-info {
      display: flex;
      flex-direction: column;
      margin-top: 4px;
      padding: 0 8px;

      .message-time {
        font-size: 11px;
        color: var(--color-background-secondary-white-600);
        opacity: 0.7;
      }
    }
  }
}

.chat-input {
  width: 100%;
  margin-bottom: 0; // Default no extra margin
  padding-bottom: env(safe-area-inset-bottom, 0); // Respect iPhone safe area
  transition: margin-bottom 0.3s ease; // Smooth transition

  // Dynamic margin when input is focused
  &.input-focused {
    margin-bottom: 40px; // Extra space for iPhone keyboard when focused
  }

  .chat-controls {
    background: var(--color-background-primary-black-400);
    border-radius: var(--radius-medium);
    padding: 0 8px;
  }

  .flex-centered {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .attach {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;

    ion-icon {
      font-size: 20px;
      color: var(--color-background-secondary-white-600);
    }
  }

  ion-input {
    flex: 1;
    --padding-start: 12px;
    --padding-end: 12px;
    --color: var(--color-background-secondary-white-600);
    --placeholder-color: var(--color-background-secondary-white-600);
    --placeholder-opacity: 0.6;
  }

  .send {
    font-size: 24px;
    color: var(--color-accent-primary-blue-600);
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }

    &:active {
      opacity: 0.6;
    }
  }

  .record {
    font-size: 24px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      color: var(--color-accent-primary-red-600);
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.9);
    }

    &.recording {
      color: var(--color-accent-primary-red-600);
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  // Beautiful recording interface (replaces input)
  .recording-interface {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(220, 38, 38, 0.03));
    border-radius: 20px;
    border: 1px solid rgba(239, 68, 68, 0.15);
    animation: recordingPulse 2s ease-in-out infinite;

    .recording-status {
      display: flex;
      align-items: center;
      gap: 12px;

      .recording-timer {
        font-size: 18px;
        font-weight: 600;
        color: var(--color-accent-primary-red-600, #DC2626);
        min-width: 50px;
        text-align: center;
        font-family: 'Courier New', monospace;
      }

      .recording-visualizer {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 0 12px;

        .sound-waves {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 2px;
          height: 100%;

          .sound-bar {
            width: 3px;
            background: linear-gradient(to top, 
              var(--color-accent-primary-red-500, #EF4444),
              var(--color-accent-primary-red-400, #F87171)
            );
            border-radius: 2px;
            min-height: 2px;
            max-height: 28px;
            transition: height 0.1s ease;
            animation: soundBarPulse 0.5s ease-in-out infinite alternate;
          }
        }
      }
    }

    .recording-actions {
      display: flex;
      justify-content: center;
      gap: 24px;

      .discard-btn {
        --padding-start: 16px;
        --padding-end: 16px;
        --padding-top: 16px;
        --padding-bottom: 16px;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        --background: transparent;
        --color: var(--color-accent-primary-red-600, #DC2626);
        transition: all 0.2s ease;
        border: 2px solid var(--color-accent-primary-red-600, #DC2626);

        &:hover {
          --background: var(--color-accent-primary-red-600, #DC2626);
          --color: white;
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        ion-icon {
          font-size: 28px;
          font-weight: bold;
        }
      }

      .done-btn {
        --padding-start: 16px;
        --padding-end: 16px;
        --padding-top: 16px;
        --padding-bottom: 16px;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        --background: transparent;
        --color: var(--color-accent-primary-green-600, #059669);
        transition: all 0.2s ease;
        border: 2px solid var(--color-accent-primary-green-600, #059669);

        &:hover {
          --background: var(--color-accent-primary-green-600, #059669);
          --color: white;
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        ion-icon {
          font-size: 28px;
          font-weight: bold;
        }
      }
    }
  }
}

// Animation for recording pulse
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Animation for recording interface
@keyframes recordingPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

// Animation for sound bars
@keyframes soundBarPulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
