<div class="smart-notes-chat">
  <!-- Messages Area -->
  <div class="messages-container">
    <div #messagesArea class="messages-area" [class.recording-mode]="isRecording">
      @if (smartNotes.length === 0) {
        <div class="empty-state">
          <ion-icon name="chatbubbles-outline"></ion-icon>
          <p>{{ 'smart-notes.empty-state' | translate }}</p>
        </div>
      } @else {
        @for (note of smartNotes; track note.id) {
          <div class="message" [class.own-message]="isCurrentUser(note)">
            <div class="message-content">
              @switch (note.type) {
                @case (SmartNoteType.TEXT) {
                  <div class="message-text">{{ note.content }}</div>
                }
                @case (SmartNoteType.PHOTO) {
                  <img 
                    [src]="note.mediaUrl" 
                    [alt]="note.content"
                    class="message-media clickable"
                    loading="lazy"
                  />
                }
                @case (SmartNoteType.VIDEO) {
                  <app-video-player
                    [videoUrl]="note.mediaUrl"
                    [showControls]="false"
                    class="message-media"
                  ></app-video-player>
                }
                @case (SmartNoteType.AUDIO) {
                  <div class="voice-message">
                    <ion-button 
                      class="voice-play-btn" 
                      fill="clear"
                      (click)="toggleVoicePlayback(note.id, note.mediaUrl)"
                    >
                      <ion-icon [name]="isVoicePlaying[note.id] ? 'pause' : 'play'"></ion-icon>
                    </ion-button>
                    
                    <div class="voice-waveform" [id]="'waveform-' + note.id"></div>
                    
                    <span class="voice-time">{{ getVoiceDurationDisplay(note.id) }}</span>
                  </div>
                }
              }
            </div>
            <div class="message-info">
              <span class="message-time">{{ note.createdAt | date:'short' }}</span>
            </div>
          </div>
        }
      }
    </div>
  </div>

  <!-- Chat Input -->
  <div class="chat-input" [class.input-focused]="isInputFocused">
    <!-- Recording interface (replaces input when recording) -->
    @if (isRecording) {
      <div class="recording-interface">
        <div class="recording-status">
          <div class="recording-timer">{{ formatDuration(recordingDuration) }}</div>
          <div class="recording-visualizer">
            <div class="sound-waves">
              @for (bar of realtimeSoundBars; track $index) {
                <div class="sound-bar" [style.height.px]="bar"></div>
              }
            </div>
          </div>
        </div>
        <div class="recording-actions">
          <ion-button fill="clear" class="discard-btn" (click)="cancelRecording()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
          <ion-button fill="clear" class="done-btn" (click)="sendRecording()">
            <ion-icon name="checkmark"></ion-icon>
          </ion-button>
        </div>
      </div>
    } @else {
      <!-- Normal input interface -->
      <div class="flex-centered chat-controls">
        @if (allowFileUpload) {
          <div (click)="triggerFileSelect()" class="attach flex-centered ion-activatable overflow-hidden">
            <ion-ripple-effect></ion-ripple-effect>
            <ion-icon src="/assets/icon/duplicate.svg"></ion-icon>
          </div>
        }
        
        <ion-input
          (keyup.enter)="handleSendMessage()"
          (ionFocus)="onInputFocus()"
          (ionBlur)="onInputBlur()"
          [(ngModel)]="message"
          [disabled]="isLoading"
          [placeholder]="placeholder"
          labelPlacement="fixed"
          type="text"
        >
        </ion-input>
        
        @if (isLoading) {
          <ion-spinner name="dots"></ion-spinner>
        } @else if (message) {
          <ion-icon class="send" name="send" (click)="handleSendMessage()"></ion-icon>
        } @else if (allowVoiceRecording) {
          <ion-icon class="record" name="mic-outline" (click)="handleStartRecording()"></ion-icon>
        }
      </div>
    }

    <!-- Hidden file input -->
    @if (allowFileUpload) {
      <input
        #fileInput
        type="file"
        accept="image/*,video/*,audio/*"
        style="display: none"
        (change)="handleFileSelect($event)"
      />
    }
  </div>
</div>
