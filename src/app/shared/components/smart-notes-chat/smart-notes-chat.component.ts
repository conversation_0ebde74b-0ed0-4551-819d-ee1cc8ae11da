import { Component, ElementRef, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

import { SmartNote, SmartNoteType } from '../../../core/models/smart-note.model';
import { FileService } from '../../../core/services/file.service';
import { ToastService } from '../../../core/services/toast.service';
import { VideoPlayerComponent } from '../video-player/video-player.component';

declare var WaveSurfer: any;

@Component({
  selector: 'app-smart-notes-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    VideoPlayerComponent
  ],
  templateUrl: './smart-notes-chat.component.html',
  styleUrls: ['./smart-notes-chat.component.scss']
})
export class SmartNotesChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesArea', { static: false }) messagesArea!: ElementRef;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;

  // Input properties
  @Input() smartNotes: SmartNote[] = [];
  @Input() currentUserId: string = '';
  @Input() placeholder: string = 'Type a message...';
  @Input() allowFileUpload: boolean = true;
  @Input() allowVoiceRecording: boolean = true;
  @Input() maxRecordingDuration: number = 300; // 5 minutes

  // Output events
  @Output() noteAdded = new EventEmitter<SmartNote>();
  @Output() notesUpdated = new EventEmitter<SmartNote[]>();

  // Chat properties
  message = '';
  isLoading = false;
  isInputFocused = false;

  // Voice recording properties
  mediaRecorder: MediaRecorder | null = null;
  audioChunks: Blob[] = [];
  recordingDuration = 0;
  recordingTimer: any;
  recordingCancelled = false;
  isRecording = false;

  // Real-time recording visualization
  realtimeSoundBars: number[] = Array(20).fill(2);
  audioContext: AudioContext | null = null;
  analyser: AnalyserNode | null = null;
  dataArray: Uint8Array | null = null;
  animationFrame: number | null = null;

  // Voice playback properties
  waveSurfers: { [key: string]: any } = {};
  isVoicePlaying: { [key: string]: boolean } = {};
  voiceProgress: { [key: string]: number } = {};
  voiceDuration: { [key: string]: number } = {};
  voiceCurrentTime: { [key: string]: number } = {};

  // Enums for template
  SmartNoteType = SmartNoteType;

  constructor(
    private fileService: FileService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    // Auto-scroll to bottom after initial load
    setTimeout(() => this.scrollToBottom(), 200);
    // Preload waveforms after a short delay
    setTimeout(() => this.preloadAudioWaveforms(), 300);
  }

  ngOnDestroy(): void {
    // Clean up all WaveSurfer instances
    this.clearAllWaveSurfers();
    
    // Clear recording timer if active
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
    
    // Stop any ongoing recording
    if (this.isRecording && this.mediaRecorder) {
      this.mediaRecorder.stop();
    }
    
    // Stop real-time visualization
    this.stopRealtimeVisualization();
  }

  // Message handling
  handleSendMessage(): void {
    if (!this.message?.trim() || this.isLoading) {
      return;
    }

    this.isLoading = true;

    const smartNote: SmartNote = {
      id: this.generateId(),
      type: SmartNoteType.TEXT,
      content: this.message.trim(),
      createdAt: new Date(),
      creatorId: this.currentUserId,
    };

    this.addNote(smartNote);
    this.message = '';
    this.isLoading = false;

    // Auto-scroll to bottom
    setTimeout(() => this.scrollToBottom(), 100);
  }

  // Focus handling
  onInputFocus(): void {
    this.isInputFocused = true;
  }

  onInputBlur(): void {
    this.isInputFocused = false;
  }

  // File upload handling
  triggerFileSelect(): void {
    if (this.allowFileUpload) {
      this.fileInput.nativeElement.click();
    }
  }

  handleFileSelect(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Reset file input
    event.target.value = '';

    this.uploadFile(file);
  }

  private uploadFile(file: File): void {
    this.isLoading = true;

    this.fileService.uploadFile(file, 'smart-notes')
      .pipe(
        switchMap((uploadResult) => {
          const smartNote: SmartNote = {
            id: this.generateId(),
            type: this.getFileType(file),
            content: file.name,
            mediaUrl: uploadResult.url,
            createdAt: new Date(),
            creatorId: this.currentUserId,
          };

          this.addNote(smartNote);

          // Auto-scroll to bottom after DOM update
          setTimeout(() => {
            this.scrollToBottom();
          }, 200);

          return of(smartNote);
        }),
        catchError((error) => {
          console.error('File upload failed:', error);
          this.toastService.showErrorToast('smart-notes.upload-error');
          return of(null);
        })
      )
      .subscribe(() => {
        this.isLoading = false;
      });
  }

  private getFileType(file: File): SmartNoteType {
    if (file.type.startsWith('image/')) {
      return SmartNoteType.PHOTO;
    } else if (file.type.startsWith('video/')) {
      return SmartNoteType.VIDEO;
    } else if (file.type.startsWith('audio/')) {
      return SmartNoteType.AUDIO;
    }
    return SmartNoteType.TEXT;
  }

  // Voice recording methods
  async handleStartRecording(): Promise<void> {
    if (!this.allowVoiceRecording) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: this.getSupportedMimeType()
      });

      // Event handlers
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processRecording();
      };

      // Reset state
      this.audioChunks = [];
      this.recordingDuration = 0;
      this.recordingCancelled = false;
      this.isRecording = true;

      // Start recording
      this.mediaRecorder.start(1000);

      // Start timer
      this.startRecordingTimer();

      // Start real-time visualization
      this.startRealtimeVisualization(stream);

    } catch (error) {
      console.error('Error starting recording:', error);
      this.toastService.showErrorToast('smart-notes.recording-error');
    }
  }

  stopRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
      this.stopRealtimeVisualization();
    }
  }

  cancelRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      // Set flag to prevent processing
      this.recordingCancelled = true;
      
      // Stop recording
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
      this.stopRealtimeVisualization();
      
      // Clear audio chunks to prevent upload
      this.audioChunks = [];
      this.recordingDuration = 0;
      
      // Stop microphone access
      const stream = this.mediaRecorder.stream;
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      
      this.toastService.showInfoToast('smart-notes.recording-cancelled');
    }
  }

  sendRecording(): void {
    this.stopRecording();
  }

  // Helper methods
  private addNote(note: SmartNote): void {
    this.smartNotes.push(note);
    this.noteAdded.emit(note);
    this.notesUpdated.emit([...this.smartNotes]);
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  isCurrentUser(note: SmartNote): boolean {
    return note.creatorId === this.currentUserId;
  }

  formatDuration(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  private scrollToBottom(): void {
    if (this.messagesArea?.nativeElement) {
      const element = this.messagesArea.nativeElement;
      
      // Force immediate scroll
      element.scrollTop = element.scrollHeight;
      
      // Also try with a small delay to ensure DOM is fully updated
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
      }, 50);
      
      // Final attempt with longer delay for complex content
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
      }, 150);
    }
  }

  // Additional methods will be added in the next part...
}
