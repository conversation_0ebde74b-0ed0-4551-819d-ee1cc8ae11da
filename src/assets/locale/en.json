{"auth": {"activate-account": "Activate account", "activate-account-success": "Account activated successfully.", "promote-to-pro": "Promote to PRO", "demote-from-pro": "Demote from PRO", "promote-to-pro-success": "Promoted to PRO successfully.", "demote-from-pro-success": "Demoted from PRO successfully.", "user": "User", "promote-to-trainer": "Promote to trainer", "demote-from-trainer": "Demote from trainer", "trainee-panel": "Trainee panel", "trainer-panel": "Trainer panel", "change-trainer": "Change trainer"}, "weight": {"weight": "Weight", "insights": "Weight insights", "today-s-weight": "Today's weight", "weight-in-kg": "Weight in KG", "weight-for": "Weight for", "weight-progress": "Weight Progress", "bw": "BW", "kg": "KG", "average-weight": "Average weight", "body-weight": "Body weight", "avg-comparison": "AVG Comparison"}, "training": {"workout": "Workout", "workout-short": "Workout", "workouts": "Workouts", "training": "Training", "training-split": "Training split", "training-splits": "Training splits", "set-current-split": "Set as current split", "set-current-split-success": "Current training split set successfully.", "create-split": "Create split", "exercise-block-select": "Select an exercise block to access options", "split": "Split", "sessions": "Sessions", "exercise": "Exercise", "exercises": "Exercises", "change-exercise": "Change exercise", "select-exercise": "Select exercise", "reps": "Reps", "reps-target-lower-bound": "Reps Target Lower Bound", "reps-target-upper-bound": "Reps Target Upper Bound", "set": "Set", "sets": "Sets", "add-set": "Add set", "remove-set": "Remove set", "start-workout": "Start workout", "active-workout": "Active workout", "resume-workout": "Resume workout", "next-workout": "Next workout", "change-workout": "Change workout", "change-workout-success": "Workout changed successfully.", "latest-workout": "Latest workout", "last-workout": "Last workout", "last-time": "Last time", "weight": "Weight", "next": "Next", "training-schedule": "Training schedule", "time-schedule": "Time schedule", "no-active-training-schedule": "No active training schedule", "no-active-training-time-schedule": "No active training schedule time", "create-training-schedule": "Create training schedule", "create-training-time-schedule": "Create training time schedule", "training-schedule-type": {"PATTERN": "Pattern", "DAYS_OF_THE_WEEK": "Days of the week"}, "training-day": "Training day", "rest-day": "Rest day", "repeat": "Repeat", "pattern-tooltip": "Pattern is a training schedule, which consists of training days and rest days. It should start with a training day and end with a rest day.", "calendar": {"header": "Calendar"}, "sets-history": {"latest-for-exercise": "Latest for exercise", "latest-for-workout": "Latest for workout", "only-for-workout": "Show only for workout", "pr": "PR", "prs": "PRs", "pr-history": "PR history"}, "muscle-group": "Muscle group", "training-volume": "Training volume", "training-volume-history": "Volume history", "copy-volume": "Copy volume", "workouts-volume": "Workouts volume", "workouts-exercises": "Workouts exercises", "exercise-notes": "Exercise notes", "workout-notes": "Workout notes", "sets-notes": "Sets notes", "compare-prs": "Compare PRs", "no-prs-made": "There are no PRs made :(", "select-trainees": "Select trainees", "exercises-history": "Exercises history", "trainee-groups": "Trainee groups", "create-workout": "Create workout", "add-exercise": "Add exercise", "no-exercises-added": "No exercises added", "select-gym": "Select a gym", "create-gym": "Create a gym", "select-gym-success": "G<PERSON> selected successfully.", "set-default-gym-confirm": "Are you sure you want to set this gym as default?", "set-default-gym-success": "Gym set as default successfully.", "gym": "Gym", "sleep": "Sleep", "recovery": "Recovery", "trainee-info": "Trainee information", "no-workouts": "It seems that you have no added workouts to your training split. You can change that from the button above - 'Edit'", "sets-type": {"label": "Sets type", "STRAIGHT": "Straight", "MAIN_BACK_OFF": "Main/Back-off", "MYO_REPS": "Myo-reps"}, "pr-message": "Congratulations! You've hit a PR!", "prs-message": "Congratulations! You've' hit {count} PRs!", "generate-feedback": "Generate feedback", "feedback-copied": "Feedback copied to clipboard.", "exact-matches": "Show only exact matches", "exact-matches-subtitle": "Show history for this exact workout only", "select-from-added-locations": "Select from added locations", "enter-location": "Enter a location", "upper-body": "Upper body", "core": "Core", "lower-body": "Lower body"}, "nutrition": {"label": "Nutrition", "foods": "Foods", "recipes": "Recipes", "log-food": "Log food", "create-food": "Create food", "create-food-from-barcode": "Create food from barcode", "meal": "<PERSON><PERSON>", "no-created-meals": "No created meals for the day", "create-meal": "Create meal", "create-meal-success": "<PERSON><PERSON> created successfully.", "log-food-success": "Food logged successfully.", "edit-food-log-success": "Food log edited successfully.", "edit-food-success": "Food edited successfully.", "delete-food-success": "Food removed successfully.", "delete-meal-success": "<PERSON><PERSON> removed successfully.", "copy-meal-success": "Me<PERSON> copied successfully.", "copy-meals-confirm": "All meals from {date} will be copied to {copyToDate}?", "copy-meals-success": "All meals copied successfully.", "change-time-meal-success": "Meal time changed successfully.", "food-barcode-not-found": "Food with barcode {barcode} wasn't found. Would you create it? 🙏🏻", "food-database-check": "Firstly, see if the food already exists in the database.", "barcode-create-success": "Barcode contributed! THANK YOU!", "food-barcode-create": "Nope, i should create it", "protein-per-100g": "Protein per 100g", "carbs-per-100g": "Carbs per 100g", "fat-per-100g": "Fat per 100g", "saturated-fat": "Saturated Fat", "monounsaturated-fat": "Monounsaturated Fat", "polyunsaturated-fat": "Polyunsaturated Fat", "trans-fat": "Trans Fat", "sugar": "Sugar", "fiber": "Fiber", "glycemic-index": "Glycemic Index", "cholesterol": "Cholesterol", "sodium": "Sodium", "potassium": "Potassium", "iron": "Iron", "calcium": "Calcium", "vitamin-a": "Vitamin A", "vitamin-b": "Vitamin B", "vitamin-c": "Vitamin C", "serving": "Serving", "brand-name": "Brand name", "food-name": "Food name", "copy-meal": "Duplicate meal to", "copy-meals": "Duplicate all meals to", "copy-food": "Duplicate food to", "latest-meal": "Latest meal", "quick-add": "Quick add", "quick-add-manual": "Manual", "quick-add-scan-photo": "AI Photo Scan", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Fat", "calories": "Calories", "my-foods": "My foods", "promotions": "Promotions", "add-promotion": "Add promotion", "add-promotions": "Add promotions", "product-promotions": {"product-name": "Product name", "store-name": "Store name", "discount-price": "Discount price", "regular-price": "Regular price", "discount": "Discount", "discount-percent": "Discount percent", "expires-today": "Изтича днес", "day-left": "Изтича утре", "days-left": "остават {days} дни", "upcoming": "Предстоящи", "active": "Активни"}, "create-product-promotion-success": "Product promotion created successfully.", "create-product-promotions-success": "Product promotions created successfully.", "edit-product-promotion-success": "Product promotion edited successfully.", "delete-product-promotion-success": "Product promotion deleted successfully.", "explore-ideas": "Explore ideas", "meal-ideas": "Meal ideas", "goal-calculator": "Goal calculator", "menus": "Menus", "meals": "Meals", "min-calories": "Minimum calories", "max-calories": "Maximum calories", "min-protein": "Minimum protein", "max-protein": "Maximum protein", "min-carbs": "Minimum carbs", "max-carbs": "Maximum carbs", "min-fat": "Minimum fat", "max-fat": "Maximum fat", "min-meals-count": "Minimum meals count", "max-meals-count": "Maximum meals count", "no-ideas-found": "0 ideas found", "ideas-search": "Search food names (,)", "number-of-servings": "No. of Servings", "nutritional-info": "Nutritional information", "additional-info": "Additional information", "add-food": "Add food", "ai-meal-recognition": "AI Meal Recognition", "ai-meal-recognition-success": "<PERSON><PERSON> recognised successfully."}, "buttons": {"archive": "Archive", "submit": "Submit", "submit-selected": "Submit selected", "logout": "Logout", "save": "Save", "finish": "Finish", "create": "Create", "create-new": "Create new", "cancel": "Cancel", "select": "Select", "cancel-select": "Cancel select", "select-all": "Select all", "deselect-all": "Deselect all", "skip": "<PERSON><PERSON>", "cancel-skip": "<PERSON><PERSON> skip", "make-rest-day": "Маке rest day", "cancel-rest-day": "Cancel rest day", "set": "Set", "edit": "Edit", "delete": "Delete", "remove": "Remove", "change-time": "Change time", "copy": "Copy", "close": "Close", "log-in-as": "Log in as", "create-new-version": "Create new version", "change-name": "Change name", "add-photo": "Add photo", "add-sound": "Add sound", "of-course": "Of course!", "share": "Share", "new-group": "New group", "edit-groups": "Edit groups", "move-up": "Move up", "move-down": "Move down", "attach-with-above": "Attach with above", "detach": "<PERSON><PERSON>", "show-filters": "Show filters", "hide-filters": "Hide filters", "clear": "Clear", "set-as-default": "Set as default", "add-card": "Add card", "renew": "<PERSON>w", "activate": "Activate", "connect": "Connect", "generate-pdfs": "Generate PDFs", "approve": "Approve", "view": "View", "join": "Join", "done": "Done", "apply": "Apply", "add": "Add", "duplicate": "Duplicate", "modify": "Modify", "generate": "Generate", "scan": "<PERSON><PERSON>"}, "home": "Home", "dashboard": "Dashboard", "avg": "AVG", "cm": "cm.", "average": "Average", "diff": "DIFF", "history": "History", "groups": "Groups", "date": "Date", "time": "Time", "details": "Details", "started-on": "Started on", "ended-on": "Ended on", "start-date": "Start date", "end-date": "End date", "name": "Name", "default": "<PERSON><PERSON><PERSON>", "skipped": "Skipped", "search": "Search", "notes": "Notes", "new-note": "New note", "video": "Video", "video-upload-success": "Video has been upload successfully.", "add-video": "Add video", "time-machine": "Time machine", "from": "From", "to": "To", "amount": "Amount", "photos": "Photos", "measurements": "Measurements", "new-measurements": "New measurements", "upload-photos": "Upload photos", "datetime": "Date and time", "status": "Status", "product": "Product", "all": "All", "progress": "Progress", "more-features": "More features", "type": "Type", "favourites": "Favourites", "options": "Options", "smart-notes": {"empty-state": "No messages yet. Start the conversation!", "uploading": "Uploading...", "upload-success": "File uploaded successfully!", "upload-error": "Failed to upload file. Please try again.", "delete-success": "Message deleted successfully!", "delete-error": "Failed to delete message. Please try again."}, "locations": "Locations", "filters": "Filters", "schedule": "Schedule", "past": "Past", "planned": "Planned", "rest": "Rest", "discounts": "Discounts", "filter": "Filter", "summary": "Summary", "visual-progress": {"label": "Visual progress", "type": {"FRONT": "Front", "BACK": "Back", "SIDEWAYS": "Sideways"}, "measurements": {"chest": "Chest", "biceps": "Biceps", "leg": "Leg", "waist": "Waist", "hips": "Hips", "chest-in-cm": "Chest in cm.", "biceps-in-cm": "Biceps in cm.", "leg-in-cm": "Leg in cm.", "waist-in-cm": "Waist in cm.", "hips-in-cm": "Hips in cm."}, "slider-view": "Slider view", "normal-view": "Normal view"}, "days": {"today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "SUNDAY": "Sunday"}, "forms": {"type": "Type", "reason": "Reason"}, "alerts": {"error": "Error", "confirm": {"main-header": "Are you sure?", "yes": "Proceed", "no": "Cancel", "cancel": "Are you sure you want to cancel?", "delete": "Are you sure you want to delete this item?", "skip-exercise": "Are you sure you want to skip this exercise?", "add-set": "Are you sure you want to add set?", "remove-set": "Are you sure you want to remove set?"}}, "muscle-groups": {"chest": "Chest", "frontDelts": "Front Delts", "lateralDelts": "Lateral Delts", "biceps": "Biceps", "abs": "Abs", "upperTraps": "Upper Traps", "rearDelts": "Rear Delts", "triceps": "Triceps", "midTraps": "Mid Traps", "lowerTraps": "Lower Traps", "lats": "Lats", "erectors": "Erectors", "quads": "Quads", "calves": "<PERSON><PERSON>", "glutes": "Glutes", "hamstrings": "Hamstrings", "forearms": "Forearms"}, "notifications": {"label": "Notifications", "send": "Send notification", "notification-send-success": "The notification was sent successfully!", "alert-header": "Your notifications are not set. Would you like to enable them?", "notifications-setup-success": "Your notifications are enabled successfully!", "special": "You have a special notification! Are you ready?"}, "times": {"just-now": "Just now", "year-ago": "{value} year ago", "years-ago": "{value} years ago", "month-ago": "{value} month ago", "months-ago": "{value} months ago", "week-ago": "{value} week ago", "weeks-ago": "{value} weeks ago", "day-ago": "{value} day ago", "days-ago": "{value} days ago", "hour-ago": "{value} hour ago", "hours-ago": "{value} hours ago", "minute-ago": "{value} minute ago", "minutes-ago": "{value} minutes ago", "second-ago": "{value} second ago", "seconds-ago": "{value} seconds ago"}, "sleep-quality": {"label": "Sleep quality", "tracking": "Sleep tracking", "chart": "Sleep stats monitoring", "update-success": "Sleep quality saved successfully.", "bedtime": "Bedtime", "wake-up-time": "Wake up time", "average-rating": "Average rating"}, "creatine-tracker": {"label": "C<PERSON><PERSON> tracker", "creatine": "<PERSON><PERSON><PERSON>", "start": "Start creatine", "rest-period": {"days": {"one": "{count} day break", "many": "{count} days break"}, "weeks": {"one": "{{count} week break", "many": "{count} weeks break"}, "months": {"one": "{count} month break", "many": "{count} months break"}}}, "payments": {"subscription": "Subscription", "subscription-plan": "Subscription plan", "subscription-plans": "Subscription plans", "free-trial": "Free trial", "free-trial-message": "You can take advantage of a free trial of the most expensive subscription plan PREMIUM for 14 days! After it expires, you decide whether to continue with it or change it. You have nothing to lose, it's free!", "subscription-status": {"ACTIVE": "Active", "CANCELLED": "Cancelled", "PENDING": "Free trial", "PAST_DUE": "Past due"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "trial-end": "Free trial end", "cards": "Cards", "add-card-success": "Card added successfully!", "card-make-active": "Make active", "card-active": "Active", "card-delete-success": "Card deleted successfully.", "card-make-active-success": "<PERSON> made active successfully.", "subscription-cancel-success": "Subscription cancelled successfully. It will be active until the end of the billing period.", "subscription-renew-success": "Subscription renewed successfully.", "subscription-activate-alert": "Are you sure you want to activate your subscription?", "subscription-activate-success": "Subscription activated successfully.", "subscription-renew-alert": "Are you sure you want to renew your subscription?", "expired-message": {"PENDING": "Your free trial has expired. You can activate your subscription with the button below. Make sure you have active stored card. Thank you!", "PAST_DUE": "Your subscription is past due. You can renew your subscription with the button below. Make sure you have active stored card. Thank you!", "CANCELLED": "Your subscription is cancelled. You can renew your subscription with the button below. Make sure you have active stored card. Thank you!"}, "create-subscription-plan": "Create subscription plan", "change-subscription-plan": "Change subscription plan", "change-plan": "Change plan", "view-plans": "View plans", "start-trial": "14-day free trial", "subscription-plan-create-success": "Subscription plan created successfully.", "change-subscription-plan-success": "Subscription plan changed successfully. The old plan will be active until the end of the billing period.", "manual-subscription-plan-success": "Manual subscription plan set successfully.", "subscription-create-success": "Subscription created successfully.", "subscription-edit-success": "Subscription edited successfully.", "verification-code": "Verification code", "verification-code-info": "Verification code has been sent to your email. It is valid for 5 minutes.", "verification-code-placeholder": "Enter code here", "payment-method": {"label": "Payment method", "CARD": "Card", "BANK_TRANSFER": "Bank transfer"}, "payment-reference": "Payment reference", "billing-information": "Billing information", "billing-information-form": {"first-name": "First name", "middle-name": "Middle name", "last-name": "Last name", "email": "Email", "city": "City", "country": "Country", "address": "Address", "postal-code": "Postal code", "phone-number": "Phone number", "phone-number-helper": "(+359)", "cyrillic-helper": "Use cyrillic letters if possible", "company-name": "Company name", "unified-id-code": "UIC", "vat-number": "VAT number", "type": {"PERSONAL": "Personal", "COMPANY": "Company"}}, "invoice-form": {"number": "Number", "product-name": "Product name", "amount": "Amount", "payment-reference": "Payment reference", "payment-method": "Payment method"}, "invoice-create-success": "Invoice created successfully.", "invoice-edit-success": "Invoice edited successfully.", "invoice-submit-confirm": "Are you sure you want to submit this invoice?", "invoice-pdf-generate-confirm": "Are you sure you want to generate PDF for this invoice?", "invoice-batch-submit-confirm": "Are you sure you want to submit this batch of invoices?", "invoice-batch-submit-success": "Batch of invoices submitted successfully.", "invoice-batch-pdfs-confirm": "Are you sure you want to generate PDFs for this batch of invoices?", "paid-feature": {"title": "Oops... Paid feature", "message": "It looks like you’ve discovered one of our premium features. Ready to level up your fitness journey? Choose the plan that works best for you!", "ESSENTIALS": {"basic-access": "Access to the main features of the application - training, nutrition, sleep, visual progress", "product-promotions": "Active and upcoming fitness product promotions from supermarkets", "history-limit": "Data history for only 3 months", "price": "Free"}, "ADVANCED": {"full-access": "Full access to all app features", "unlimited-history": "No data history limit", "discord-community-access": "Access to Discord community", "explore-ideas": "Explore ideas", "meal-copying": "Copy meals and diaries", "quick-add-macros": "Quick add macros", "creatine-tracking": "Creatine tracking", "time-machine": "Time machine", "gym-tracking": "Tracking workouts in different gyms", "price": "16.99 BGN", "yearly-price": "99.99 BGN", "regular-yearly-price": "199.99 BGN", "price-per-day": "(0.56 BGN per day)", "yearly-price-per-day": "(0.27 BGN per day)"}, "PREMIUM": {"everything-from-advanced": "Everything from Advanced", "whoop-integration": "Whoop integration", "apple-google-integration": "Future Apple Health and Google Health Connect integration", "price": "29.99 BGN", "yearly-price": "179.99 BGN", "regular-yearly-price": "359.99 BGN", "price-per-day": "(0.99 BGN per day)", "yearly-price-per-day": "(0.49 BGN per day)"}}, "yearly": "Yearly"}, "integrations": {"label": "Integrations", "connected": "Connected", "whoop-success": "<PERSON><PERSON> connected successfully.", "discord-success": "You have been successfully added to the Discord server."}, "invitations": {"label": "Invitations", "create": "Create invitation", "link-copied": "Invitation link copied to clipboard.", "approve-confirm": "Are you sure you want to approve this invitation?", "decline-confirm": "Are you sure you want to decline this invitation?", "decline-success": "Invitation declined successfully."}, "courses": {"label": "Courses", "create": "Create course", "courses-access": "Courses access", "courses-access-success": "Courses access set successfully.", "create-success": "Course created successfully.", "delete-success": "Course deleted successfully.", "mark-as-watched": "<PERSON> as watched", "mark-as-unwatched": "<PERSON> as unwatched", "mark-as-watched-success": "Course marked as watched successfully.", "mark-as-unwatched-success": "Course marked as unwatched successfully.", "refresh-course-confirm": "Are you sure you want to send a notification for new videos to all trainees with this course?", "refresh-course-success": "Notifications sent successfully."}, "chat": {"placeholder": "Type a message..."}}