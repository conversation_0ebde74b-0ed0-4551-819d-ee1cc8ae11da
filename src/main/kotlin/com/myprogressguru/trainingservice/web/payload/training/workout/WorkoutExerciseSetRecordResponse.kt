package com.myprogressguru.trainingservice.web.payload.training.workout

import com.myprogressguru.trainingservice.entity.projection.SetRecord

data class WorkoutExerciseSetRecordResponse(

    val id: String,

    override val orderNumber: Int,

    val repsTarget: Double?,

    val weightInKgTarget: Double?,

    override val reps: Double?,

    override val weightInKg: Double?,

    val exerciseRecord: WorkoutExerciseRecordInfoResponse,

    var bodyWeightInKg: Double?,

    var sleepQuality: Int?,

    val video: WorkoutExerciseSetRecordVideoResponse?,

    @get:JvmName("getIsPersonalRecord")
    val isPersonalRecord: Boolean

) : SetRecord
