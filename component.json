{"hasImages": false, "componentUIDLs": [{"name": "Cha<PERSON>", "node": {"type": "element", "content": {"elementType": "container", "name": "Cha<PERSON>", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(2, 2, 2, 1)"}, "width": {"type": "static", "content": "100%"}, "height": {"type": "static", "content": "auto"}, "display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "16px"}, "padding": {"type": "static", "content": "0 24px 59px"}, "flexShrink": {"type": "static", "content": "0"}, "borderRadius": {"type": "static", "content": "47px"}, "overflow": {"type": "static", "content": "hidden"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "SectionModal", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(7, 7, 7, 1)"}, "width": {"type": "static", "content": "430px"}, "display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "16px"}, "padding": {"type": "static", "content": "59px 0 24px"}, "flexShrink": {"type": "static", "content": "0"}, "borderRadius": {"type": "static", "content": "16px"}, "zIndex": {"type": "static", "content": 0}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Header", "abilities": {}, "style": {"width": {"type": "static", "content": "382px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "space-between"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "89px"}, "padding": {"type": "static", "content": "8px 0"}, "flexShrink": {"type": "static", "content": "0"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "<PERSON><PERSON><PERSON><PERSON>", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "flex-start"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesDisplaysub-headline-m-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesDisplaysub-headline-m-light"}}}, "style": {"color": {"type": "static", "content": "rgba(102, 102, 102, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "center"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "Smart notes"}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesDisplayheadline-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesDisplayheadline-light"}}}, "style": {"color": {"type": "static", "content": "rgba(244, 244, 255, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "Exercise Name"}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "IconL", "abilities": {}, "style": {"width": {"type": "static", "content": "32px"}, "height": {"type": "static", "content": "32px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "20px"}, "padding": {"type": "static", "content": "6px"}, "flexShrink": {"type": "static", "content": "0"}, "overflow": {"type": "static", "content": "hidden"}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "<PERSON>ame", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPGcgY2xpcC1wYXRoPSd1cmwoI2NsaXAwXzMxMzVfMzA0NyknPgo8cGF0aCBkPSdNMTggNkw2IDE4JyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS13aWR0aD0nMS41JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnLz4KPHBhdGggZD0nTTYgNkwxOCAxOCcgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2Utd2lkdGg9JzEuNScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJy8+CjwvZz4KPGRlZnM+CjxjbGlwUGF0aCBpZD0nY2xpcDBfMzEzNV8zMDQ3Jz4KPHJlY3Qgd2lkdGg9JzI0JyBoZWlnaHQ9JzI0JyBmaWxsPSd3aGl0ZScvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo="}, "alt": {"type": "static", "content": "FrameI2458"}}, "style": {"width": {"type": "static", "content": "24px"}, "height": {"type": "static", "content": "24px"}}}}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "Sets", "abilities": {}, "style": {"width": {"type": "static", "content": "382px"}, "display": {"type": "static", "content": "flex"}, "alignItems": {"type": "static", "content": "flex-start"}, "gap": {"type": "static", "content": "16px"}, "flexShrink": {"type": "static", "content": "0"}}, "children": []}}]}}, {"type": "element", "content": {"elementType": "container", "name": "Cha<PERSON>", "abilities": {}, "style": {"height": {"type": "static", "content": "622px"}, "display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "alignItems": {"type": "static", "content": "flex-start"}, "gap": {"type": "static", "content": "8px"}, "flexShrink": {"type": "static", "content": "0"}, "alignSelf": {"type": "static", "content": "stretch"}, "zIndex": {"type": "static", "content": 1}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Left", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "alignItems": {"type": "static", "content": "flex-end"}, "gap": {"type": "static", "content": "6px"}, "alignSelf": {"type": "static", "content": "stretch"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Info", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "flex-end"}, "alignItems": {"type": "static", "content": "flex-start"}, "gap": {"type": "static", "content": "4px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(191, 191, 191, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "@Zori"}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(77, 76, 76, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": " Today 11:47"}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "<PERSON><PERSON><PERSON><PERSON>", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(30, 30, 30, 1)"}, "width": {"type": "static", "content": "90px"}, "height": {"type": "static", "content": "160px"}, "display": {"type": "static", "content": "flex"}, "alignItems": {"type": "static", "content": "flex-start"}, "flexShrink": {"type": "static", "content": "0"}, "borderRadius": {"type": "static", "content": "16px 6px 16px 16px"}, "position": {"type": "static", "content": "relative"}, "overflow": {"type": "static", "content": "hidden"}}, "children": [{"type": "element", "content": {"elementType": "button", "name": "<PERSON><PERSON>", "abilities": {}, "style": {"width": {"type": "static", "content": "39px"}, "display": {"type": "static", "content": "flex"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "10px"}, "padding": {"type": "static", "content": "10px"}, "position": {"type": "static", "content": "absolute"}, "top": {"type": "static", "content": "0px"}, "left": {"type": "static", "content": "50.75px"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "IconS", "abilities": {}, "style": {"width": {"type": "static", "content": "19.25px"}, "height": {"type": "static", "content": "14px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8.750000953674316px"}, "flexShrink": {"type": "static", "content": "0"}, "overflow": {"type": "static", "content": "hidden"}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "playerplay1", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTUnIGhlaWdodD0nMTQnIHZpZXdCb3g9JzAgMCAxNSAxNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPGcgY2xpcC1wYXRoPSd1cmwoI2NsaXAwXzMxMzVfNTMxOCknPgo8cGF0aCBkPSdNNC40NTgyNSAyLjMzMzNWMTEuNjY2NkwxMi4wNDE2IDYuOTk5OTZMNC40NTgyNSAyLjMzMzNaJyBmaWxsPScjRjVGNUZGJy8+CjwvZz4KPGRlZnM+CjxjbGlwUGF0aCBpZD0nY2xpcDBfMzEzNV81MzE4Jz4KPHJlY3Qgd2lkdGg9JzE0JyBoZWlnaHQ9JzE0JyBmaWxsPSd3aGl0ZScgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMC4zNzUpJy8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg=="}, "alt": {"type": "static", "content": "playerplay1I2458"}}, "style": {"width": {"type": "static", "content": "14px"}, "height": {"type": "static", "content": "14px"}}}}]}}]}}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "Right", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "flex-start"}, "gap": {"type": "static", "content": "6px"}, "alignSelf": {"type": "static", "content": "stretch"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Info", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "4px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(191, 191, 191, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "@Aleksandar"}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(102, 102, 102, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": " Today 11:52"}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "Note", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(30, 30, 30, 1)"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8px"}, "padding": {"type": "static", "content": "8px"}, "alignSelf": {"type": "static", "content": "stretch"}, "borderRadius": {"type": "static", "content": "24px 24px 24px 6px"}}, "children": [{"type": "element", "content": {"elementType": "button", "name": "<PERSON><PERSON>", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(242, 242, 242, 1)"}, "width": {"type": "static", "content": "24px"}, "height": {"type": "static", "content": "24px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "4px"}, "padding": {"type": "static", "content": "4px 8px"}, "flexShrink": {"type": "static", "content": "0"}, "borderRadius": {"type": "static", "content": "12px"}, "position": {"type": "static", "content": "relative"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "IconS", "abilities": {}, "style": {"width": {"type": "static", "content": "20px"}, "height": {"type": "static", "content": "20px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8.750000953674316px"}, "flexShrink": {"type": "static", "content": "0"}, "overflow": {"type": "static", "content": "hidden"}, "zIndex": {"type": "static", "content": 0}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "playerplay1", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHZpZXdCb3g9JzAgMCAxNCAxNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPGcgY2xpcC1wYXRoPSd1cmwoI2NsaXAwXzMxMzVfNTMyMiknPgo8cGF0aCBkPSdNNC4wODMzNyAyLjMzMzI4VjExLjY2NjZMMTEuNjY2NyA2Ljk5OTk1TDQuMDgzMzcgMi4zMzMyOFonIGZpbGw9JyMxRjFGMUYnLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSdjbGlwMF8zMTM1XzUzMjInPgo8cmVjdCB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIGZpbGw9J3doaXRlJy8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg=="}, "alt": {"type": "static", "content": "playerplay1I2458"}}, "style": {"width": {"type": "static", "content": "14px"}, "height": {"type": "static", "content": "14px"}}}}]}}, {"type": "element", "content": {"elementType": "image", "name": "Ellipse4", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAQSURBVHgBAQUA+v8A8/P/QAjoAyYWlNjkAAAAAElFTkSuQmCC"}, "alt": {"type": "static", "content": "Ellipse4I2458"}}, "style": {"width": {"type": "static", "content": "4px"}, "height": {"type": "static", "content": "4px"}, "position": {"type": "static", "content": "absolute"}, "top": {"type": "static", "content": "10px"}, "left": {"type": "static", "content": "10px"}, "zIndex": {"type": "static", "content": 1}}}}]}}, {"type": "element", "content": {"elementType": "image", "name": "Lines", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTEwJyBoZWlnaHQ9JzE4JyB2aWV3Qm94PScwIDAgMTEwIDE4JyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPgo8cGF0aCBkPSdNMSA3VjExJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNNSA3VjExJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNOSA1VjEzJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNMTMgMVYxNycgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTE3IDFWMTcnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J00yMSA1VjEzJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNMjUgN1YxMScgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTI5IDdWMTEnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J00zMyA4VjEwJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNMzcgOFYxMCcgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTQxIDhWMTAnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J000NSA4VjEwJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNNDkgOFYxMCcgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTUzIDhWMTAnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J001NyA4VjEwJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNNjEgOFYxMCcgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTY1IDdWMTEnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J002OSA3VjExJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNNzMgM1YxNScgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTc3IDFWMTcnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J004MSAzVjE1JyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNODUgN1YxMScgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTg5IDRWMTQnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J005MyA0VjE0JyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNOTcgNFYxNCcgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPHBhdGggZD0nTTEwMSA3VjExJyBzdHJva2U9JyNGNUY1RkYnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcvPgo8cGF0aCBkPSdNMTA1IDRWMTQnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJy8+CjxwYXRoIGQ9J00xMDkgMVYxNycgc3Ryb2tlPScjRjVGNUZGJyBzdHJva2UtbGluZWNhcD0ncm91bmQnLz4KPC9zdmc+Cg=="}, "alt": {"type": "static", "content": "Lines2458"}}, "style": {"width": {"type": "static", "content": "108px"}, "height": {"type": "static", "content": "16px"}}}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l"}}}, "style": {"color": {"type": "static", "content": "rgba(244, 244, 255, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "center"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "2:02"}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "NoteSent", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(30, 30, 30, 1)"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "flex-end"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8px"}, "padding": {"type": "static", "content": "12px 16px"}, "borderRadius": {"type": "static", "content": "6px 16px 16px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l"}}}, "style": {"color": {"type": "static", "content": "rgba(244, 244, 255, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "Try more, hold onto it"}]}}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "Left2", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "alignItems": {"type": "static", "content": "flex-end"}, "gap": {"type": "static", "content": "6px"}, "alignSelf": {"type": "static", "content": "stretch"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Info", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "flex-end"}, "alignItems": {"type": "static", "content": "flex-start"}, "gap": {"type": "static", "content": "4px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(191, 191, 191, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "@Zori"}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(102, 102, 102, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": " Today 11:52"}]}}]}}, {"type": "element", "content": {"elementType": "button", "name": "<PERSON><PERSON>", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(242, 242, 242, 1)"}, "height": {"type": "static", "content": "24px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "4px"}, "padding": {"type": "static", "content": "4px 10px 4px 8px"}, "flexShrink": {"type": "static", "content": "0"}, "borderRadius": {"type": "static", "content": "12px 12px 4px"}, "position": {"type": "static", "content": "relative"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "IconS", "abilities": {}, "style": {"width": {"type": "static", "content": "20px"}, "height": {"type": "static", "content": "20px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8.750000953674316px"}, "flexShrink": {"type": "static", "content": "0"}, "overflow": {"type": "static", "content": "hidden"}, "zIndex": {"type": "static", "content": 0}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "playerplay1", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHZpZXdCb3g9JzAgMCAxNCAxNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPGcgY2xpcC1wYXRoPSd1cmwoI2NsaXAwXzMxMzVfMjk2NCknPgo8cGF0aCBkPSdNNC4wODMyNSAyLjMzMzMxVjExLjY2NjZMMTEuNjY2NiA2Ljk5OTk4TDQuMDgzMjUgMi4zMzMzMVonIGZpbGw9JyMwODA4MDgnLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSdjbGlwMF8zMTM1XzI5NjQnPgo8cmVjdCB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIGZpbGw9J3doaXRlJy8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg=="}, "alt": {"type": "static", "content": "playerplay1I2458"}}, "style": {"width": {"type": "static", "content": "14px"}, "height": {"type": "static", "content": "14px"}}}}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l"}}}, "style": {"color": {"type": "static", "content": "rgba(7, 7, 7, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "center"}, "lineHeight": {"type": "static", "content": "normal"}, "zIndex": {"type": "static", "content": 1}}, "children": [{"type": "static", "content": "Preview"}]}}, {"type": "element", "content": {"elementType": "image", "name": "Ellipse4", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAQSURBVHgBAQUA+v8A8/P/QAjoAyYWlNjkAAAAAElFTkSuQmCC"}, "alt": {"type": "static", "content": "Ellipse4I2458"}}, "style": {"width": {"type": "static", "content": "4px"}, "height": {"type": "static", "content": "4px"}, "position": {"type": "static", "content": "absolute"}, "top": {"type": "static", "content": "10px"}, "left": {"type": "static", "content": "37px"}, "zIndex": {"type": "static", "content": 2}}}}]}}, {"type": "element", "content": {"elementType": "container", "name": "NoteReceived", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(242, 242, 242, 1)"}, "display": {"type": "static", "content": "flex"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8px"}, "padding": {"type": "static", "content": "12px 16px"}, "borderRadius": {"type": "static", "content": "16px 6px 16px 16px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l"}}}, "style": {"color": {"type": "static", "content": "rgba(7, 7, 7, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "Giving it all"}]}}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "Right2", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "flexDirection": {"type": "static", "content": "column"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "flex-start"}, "gap": {"type": "static", "content": "6px"}, "alignSelf": {"type": "static", "content": "stretch"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Info", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "4px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(191, 191, 191, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "@Aleksandar"}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l-light": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l-light"}}}, "style": {"color": {"type": "static", "content": "rgba(102, 102, 102, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": " Today 11:52"}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "NoteSent", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(30, 30, 30, 1)"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "flex-end"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "8px"}, "padding": {"type": "static", "content": "12px 16px"}, "borderRadius": {"type": "static", "content": "16px 16px 16px 6px"}}, "children": [{"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesBodyTextbody-text-l": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesBodyTextbody-text-l"}}}, "style": {"color": {"type": "static", "content": "rgba(244, 244, 255, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}}, "children": [{"type": "static", "content": "Keep it up."}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "TypingIndicator", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(30, 30, 30, 1)"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "flex-end"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "4px"}, "padding": {"type": "static", "content": "12px 16px"}, "borderRadius": {"type": "static", "content": "6px 16px 16px"}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "Ellipse2", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAGCAYAAADgzO9IAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABbSURBVHgBdYwxCsAgDEX1i3fo4NAhi+D9TyK4ODg4eIhqmnQqgg8C4cF/1gillHutFfRn5pFSqjbnTM65T/5o8N5fmzQAAswBSLvvUtIDMcY25+wyf/S0T0T1BfXkHSp+BJJdAAAAAElFTkSuQmCC"}, "alt": {"type": "static", "content": "Ellipse2I2458"}}, "style": {"width": {"type": "static", "content": "6px"}, "height": {"type": "static", "content": "6px"}}}}, {"type": "element", "content": {"elementType": "image", "name": "Ellipse3", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAGCAYAAADgzO9IAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABVSURBVHgBdYlRDYBQCACBALZgRLKADQygFngFtIAWYowWBmDypm5O533d7hASVe2JqIOTTUQGzDg94k1BM9OU5jV2gh/qWD6RaMUq7j5GRHvFmZnLAcXuFrTZAQOGAAAAAElFTkSuQmCC"}, "alt": {"type": "static", "content": "Ellipse3I2458"}}, "style": {"width": {"type": "static", "content": "6px"}, "height": {"type": "static", "content": "6px"}}}}, {"type": "element", "content": {"elementType": "image", "name": "Ellipse4", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAGCAYAAADgzO9IAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABbSURBVHgBdYwxCsAgDEX1i3fo4NAhi+D9TyK4ODg4eIhqmnQqgg8C4cF/1gillHutFfRn5pFSqjbnTM65T/5o8N5fmzQAAswBSLvvUtIDMcY25+wyf/S0T0T1BfXkHSp+BJJdAAAAAElFTkSuQmCC"}, "alt": {"type": "static", "content": "Ellipse4I2458"}}, "style": {"width": {"type": "static", "content": "6px"}, "height": {"type": "static", "content": "6px"}}}}]}}]}}]}}, {"type": "element", "content": {"elementType": "container", "name": "TextField", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(15, 15, 15, 1)"}, "display": {"type": "static", "content": "flex"}, "alignItems": {"type": "static", "content": "center"}, "padding": {"type": "static", "content": "8px"}, "flexShrink": {"type": "static", "content": "0"}, "alignSelf": {"type": "static", "content": "stretch"}, "borderRadius": {"type": "static", "content": "16px"}, "zIndex": {"type": "static", "content": 2}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "Section", "abilities": {}, "style": {"display": {"type": "static", "content": "flex"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "16px"}, "flexGrow": {"type": "static", "content": "1"}}, "children": [{"type": "element", "content": {"elementType": "button", "name": "<PERSON><PERSON>", "abilities": {}, "style": {"backgroundColor": {"type": "static", "content": "rgba(242, 242, 242, 1)"}, "width": {"type": "static", "content": "24px"}, "height": {"type": "static", "content": "24px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "4px"}, "padding": {"type": "static", "content": "4px 8px"}, "flexShrink": {"type": "static", "content": "0"}, "borderRadius": {"type": "static", "content": "6px"}, "borderWidth": {"type": "static", "content": "0.5px"}, "borderColor": {"type": "static", "content": "rgba(242, 242, 242, 1)"}, "borderStyle": {"type": "static", "content": "solid"}, "position": {"type": "static", "content": "relative"}}, "children": [{"type": "element", "content": {"elementType": "container", "name": "IconS", "abilities": {}, "style": {"width": {"type": "static", "content": "20px"}, "height": {"type": "static", "content": "20px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "11.666667938232422px"}, "flexShrink": {"type": "static", "content": "0"}, "overflow": {"type": "static", "content": "hidden"}, "zIndex": {"type": "static", "content": 0}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "duplicate1", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHZpZXdCb3g9JzAgMCAxNCAxNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPGcgY2xpcC1wYXRoPSd1cmwoI2NsaXAwXzMxMzVfMzg4NCknPgo8cGF0aCBkPSdNNS4wMzEyNSAzLjA3NjE3SDExLjE1NjJDMTEuNjA5OSAzLjA3NjE3IDEyLjA0NzYgMy4yMzM1MyAxMi4zOTU1IDMuNTE4NTVMMTIuNTM5MSAzLjY0ODQ0QzEyLjkwNTcgNC4wMTUwOSAxMy4xMTEzIDQuNTEyNzMgMTMuMTExMyA1LjAzMTI1VjExLjE1NjJDMTMuMTExMyAxMS42NzQ4IDEyLjkwNTcgMTIuMTcyNCAxMi41MzkxIDEyLjUzOTFDMTIuMTcyNCAxMi45MDU3IDExLjY3NDggMTMuMTExMyAxMS4xNTYyIDEzLjExMTNINS4wMzEyNUM0LjU3NzU3IDEzLjExMTMgNC4xMzk4OCAxMi45NTQgMy43OTE5OSAxMi42Njg5TDMuNjQ4NDQgMTIuNTM5MUMzLjI4MTc5IDEyLjE3MjQgMy4wNzYxNyAxMS42NzQ4IDMuMDc2MTcgMTEuMTU2MlY1LjAzMTI1QzMuMDc2MTcgNC41Nzc1NyAzLjIzMzUzIDQuMTM5ODggMy41MTg1NSAzLjc5MTk5TDMuNjQ4NDQgMy42NDg0NEM0LjAxNTA5IDMuMjgxNzkgNC41MTI3MyAzLjA3NjE3IDUuMDMxMjUgMy4wNzYxN1pNOC4wNzIyNyA1LjQ1NjA1QzcuODI5MTEgNS40NjgyIDcuNjQyNTggNS42NzY2OSA3LjY0MjU4IDUuOTE4OTVWNy42NDI1OEg1LjkwNjI1TDUuODE1NDMgNy42NTEzN0w1LjcyODUyIDcuNjc4NzFDNS42NzI1MyA3LjcwMjcgNS42MjIxNCA3LjczODE4IDUuNTgwMDggNy43ODIyM0M1LjUzNzk1IDcuODI2NDQgNS41MDQ3MiA3Ljg3OTI5IDUuNDgzNCA3LjkzNjUyQzUuNDYyMTcgNy45OTM2MiA1LjQ1MzE1IDguMDU0MzkgNS40NTYwNSA4LjExNTIzTDUuNDY4NzUgOC4yMDQxQzUuNTIwMiA4LjQwMjUyIDUuNzA3MDYgOC41NDQ5MiA1LjkxODk1IDguNTQ0OTJINy42NDI1OFYxMC4yODEyTDcuNjUxMzcgMTAuMzcyMUw3LjY3ODcxIDEwLjQ1OUM3LjcwMjcgMTAuNTE1IDcuNzM4MTggMTAuNTY1NCA3Ljc4MjIzIDEwLjYwNzRDNy44MDQzNyAxMC42Mjg1IDcuODI4NjggMTAuNjQ3OSA3Ljg1NDQ5IDEwLjY2NDFMNy45MzY1MiAxMC43MDQxQzcuOTY1MDIgMTAuNzE0NyA3Ljk5NDUyIDEwLjcyMTkgOC4wMjQ0MSAxMC43MjY2TDguMTE1MjMgMTAuNzMxNEM4LjM1ODM5IDEwLjcxOTMgOC41NDQ5MiAxMC41MTA4IDguNTQ0OTIgMTAuMjY4NlY4LjU0NDkySDEwLjI2ODZDMTAuNTEwOCA4LjU0NDkyIDEwLjcyMDYgOC4zNTg0NCAxMC43MzI0IDguMTE1MjNMMTAuNzI3NSA4LjAyNDQxTDEwLjcwNDEgNy45MzY1MkMxMC42OTM0IDcuOTA3OTIgMTAuNjgwMiA3Ljg4MDI0IDEwLjY2NDEgNy44NTQ0OUwxMC42MDg0IDcuNzgyMjNDMTAuNTg3MyA3Ljc2MDEgMTAuNTY0MSA3LjczOTk5IDEwLjUzOTEgNy43MjI2NkwxMC40NTkgNy42Nzg3MUMxMC40MDI4IDcuNjU0NzMgMTAuMzQyMyA3LjY0MjUxIDEwLjI4MTIgNy42NDI1OEg4LjU0NDkyVjUuOTA2MjVMOC41MzYxMyA1LjgxNTQzTDguNTA4NzkgNS43Mjg1MkM4LjQ4NDggNS42NzI1MyA4LjQ0OTMyIDUuNjIyMTQgOC40MDUyNyA1LjU4MDA4QzguMzYxMDYgNS41Mzc5NSA4LjMwODIxIDUuNTA0NzIgOC4yNTA5OCA1LjQ4MzRDOC4yMjI0NyA1LjQ3MjggOC4xOTI5OCA1LjQ2NTU5IDguMTYzMDkgNS40NjA5NEw4LjA3MjI3IDUuNDU2MDVaJyBmaWxsPScjMDIwMjAyJyBzdHJva2U9JyMwMjAyMDInIHN0cm9rZS13aWR0aD0nMC4wMjczNDM4Jy8+CjxwYXRoIGQ9J00xMC44MjQ4IDIuMTg3NUMxMC42ODg3IDEuODAzOTMgMTAuNDM3MyAxLjQ3MTg4IDEwLjEwNDkgMS4yMzY4OUM5Ljc3MjYzIDEuMDAxOSA5LjM3NTc1IDAuODc1NDkxIDguOTY4NzUgMC44NzVIMi44NDM3NUMyLjMyMTYgMC44NzUgMS44MjA4NSAxLjA4MjQyIDEuNDUxNjMgMS40NTE2M0MxLjA4MjQyIDEuODIwODUgMC44NzUgMi4zMjE2IDAuODc1IDIuODQzNzVWOC45Njg3NUMwLjg3NTQ5MSA5LjM3NTc1IDEuMDAxOSA5Ljc3MjYzIDEuMjM2ODkgMTAuMTA0OUMxLjQ3MTg4IDEwLjQzNzMgMS44MDM5MyAxMC42ODg3IDIuMTg3NSAxMC44MjQ4VjQuMzc1QzIuMTg3NSAzLjc5NDg0IDIuNDE3OTcgMy4yMzg0NCAyLjgyODIgMi44MjgyQzMuMjM4NDQgMi40MTc5NyAzLjc5NDg0IDIuMTg3NSA0LjM3NSAyLjE4NzVIMTAuODI0OFonIGZpbGw9JyMwMjAyMDInLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSdjbGlwMF8zMTM1XzM4ODQnPgo8cmVjdCB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIGZpbGw9J3doaXRlJy8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg=="}, "alt": {"type": "static", "content": "duplicate1I2458"}}, "style": {"width": {"type": "static", "content": "14px"}, "height": {"type": "static", "content": "14px"}}}}]}}, {"type": "element", "content": {"elementType": "image", "name": "Ellipse4", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAQSURBVHgBAQUA+v8AAAAAAAAFAAFkeJU4AAAAAElFTkSuQmCC"}, "alt": {"type": "static", "content": "Ellipse4I2458"}}, "style": {"width": {"type": "static", "content": "4px"}, "height": {"type": "static", "content": "4px"}, "position": {"type": "static", "content": "absolute"}, "top": {"type": "static", "content": "10px"}, "left": {"type": "static", "content": "10px"}, "zIndex": {"type": "static", "content": 1}}}}]}}, {"type": "element", "content": {"elementType": "text", "semanticType": "span", "name": "text", "referencedStyles": {"GuruTypefacesDisplaysub-headline-s": {"type": "style-map", "content": {"mapType": "project-referenced", "referenceId": "GuruTypefacesDisplaysub-headline-s"}}}, "style": {"color": {"type": "static", "content": "rgba(102, 102, 102, 1)"}, "height": {"type": "static", "content": "auto"}, "textAlign": {"type": "static", "content": "left"}, "lineHeight": {"type": "static", "content": "normal"}, "flexGrow": {"type": "static", "content": 1}}, "children": [{"type": "static", "content": "Write a message..."}]}}, {"type": "element", "content": {"elementType": "container", "name": "IconM", "abilities": {}, "style": {"width": {"type": "static", "content": "24px"}, "height": {"type": "static", "content": "24px"}, "display": {"type": "static", "content": "flex"}, "justifyContent": {"type": "static", "content": "center"}, "alignItems": {"type": "static", "content": "center"}, "gap": {"type": "static", "content": "15px"}, "padding": {"type": "static", "content": "4.5px"}, "flexShrink": {"type": "static", "content": "0"}, "overflow": {"type": "static", "content": "hidden"}}, "children": [{"type": "element", "content": {"elementType": "image", "name": "micoutline1", "abilities": {}, "attrs": {"src": {"type": "static", "content": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAnIGhlaWdodD0nMjAnIHZpZXdCb3g9JzAgMCAyMCAyMCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPHBhdGggZD0nTTcuNSAxNy41SDEyLjVNMTUgOC4xMjVWOS4zNzVDMTUgMTIuMTI1IDEyLjc1IDE0LjM3NSAxMCAxNC4zNzVNMTAgMTQuMzc1QzcuMjUgMTQuMzc1IDUgMTIuMTI1IDUgOS4zNzVWOC4xMjVNMTAgMTQuMzc1VjE3LjUnIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLXdpZHRoPScxLjI1JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnLz4KPHBhdGggZD0nTTEwIDIuNDk5OTdDOS42NzEyNiAyLjQ5ODMxIDkuMzQ1NDMgMi41NjE4NSA5LjA0MTM3IDIuNjg2OUM4LjczNzMgMi44MTE5NSA4LjQ2MTA1IDIuOTk2MDMgOC4yMjg1NyAzLjIyODUxQzcuOTk2MSAzLjQ2MDk5IDcuODEyMDEgMy43MzcyNCA3LjY4Njk2IDQuMDQxMzFDNy41NjE5MSA0LjM0NTM3IDcuNDk4MzggNC42NzEyIDcuNTAwMDMgNC45OTk5N1Y5LjMzNTkxQzcuNTAwMDMgMTAuNzEwOSA4LjYzMjg0IDExLjg3NSAxMCAxMS44NzVDMTEuMzY3MiAxMS44NzUgMTIuNSAxMC43NDIyIDEyLjUgOS4zMzU5MVY0Ljk5OTk3QzEyLjUgMy41OTM3MiAxMS40MDYzIDIuNDk5OTcgMTAgMi40OTk5N1onIHN0cm9rZT0nI0Y1RjVGRicgc3Ryb2tlLXdpZHRoPScxLjI1JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnLz4KPC9zdmc+Cg=="}, "alt": {"type": "static", "content": "micoutline1I2458"}}, "style": {"width": {"type": "static", "content": "20px"}, "height": {"type": "static", "content": "20px"}}}}]}}]}}]}}]}}}], "components": {}, "usedDesignTokens": {}, "usedTextStyles": {"GuruTypefacesDisplayheadline-light": {"id": "S:1594d44bf0a7bcf2f78d8f19fc9c116189d46e66,", "type": "reusable-project-style-map", "content": {"fontSize": {"type": "static", "content": "24px"}, "fontFamily": {"type": "static", "content": "Bahnschrift"}, "fontStyle": {"type": "static", "content": "normal"}, "fontWeight": {"type": "static", "content": "300px"}, "fontStretch": {"type": "static", "content": "normal"}, "letterSpacing": {"type": "static", "content": "0em"}}}, "GuruTypefacesDisplaysub-headline-m-light": {"id": "S:6d2cea84440b85fbf67e8e476cbf6b328ff0be68,", "type": "reusable-project-style-map", "content": {"fontSize": {"type": "static", "content": "16px"}, "fontFamily": {"type": "static", "content": "Bahnschrift"}, "fontStyle": {"type": "static", "content": "normal"}, "fontWeight": {"type": "static", "content": "300px"}, "fontStretch": {"type": "static", "content": "normal"}, "letterSpacing": {"type": "static", "content": "0em"}}}, "GuruTypefacesDisplaysub-headline-s": {"id": "S:5e5b976a2d33c99dea79fcd4ce0e48c8c963b9fb,", "type": "reusable-project-style-map", "content": {"fontSize": {"type": "static", "content": "12px"}, "fontFamily": {"type": "static", "content": "Bahnschrift"}, "fontStyle": {"type": "static", "content": "normal"}, "fontWeight": {"type": "static", "content": "300px"}, "fontStretch": {"type": "static", "content": "normal"}, "letterSpacing": {"type": "static", "content": "0em"}}}, "GuruTypefacesBodyTextbody-text-l-light": {"id": "S:33d131b913db5a1261eb966c48f5f11d9d08c693,", "type": "reusable-project-style-map", "content": {"fontSize": {"type": "static", "content": "10px"}, "fontFamily": {"type": "static", "content": "Bahnschrift"}, "fontStyle": {"type": "static", "content": "normal"}, "fontWeight": {"type": "static", "content": "300px"}, "fontStretch": {"type": "static", "content": "normal"}, "letterSpacing": {"type": "static", "content": "0em"}}}, "GuruTypefacesBodyTextbody-text-l": {"id": "S:75307a300953b93d0409c7cc042e4ff4eb151e81,", "type": "reusable-project-style-map", "content": {"fontSize": {"type": "static", "content": "10px"}, "fontFamily": {"type": "static", "content": "Bahnschrift"}, "fontStyle": {"type": "static", "content": "normal"}, "fontWeight": {"type": "static", "content": "400px"}, "fontStretch": {"type": "static", "content": "normal"}, "letterSpacing": {"type": "static", "content": "0em"}}}}, "usedClasses": {}, "globals": {"settings": {"title": "exported project", "language": "english"}, "meta": [], "assets": []}}